import asyncio

from langgraph_sdk import get_client

client = get_client(url="http://localhost:2024")

import os
import uuid

thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"


async def main():
    thread = await client.threads.create(if_exists="raise", thread_id=thread_id)
    print(f"created thread: {thread}")
    while True:
        try:
            human_input = input("human: ")
            prompt = {"messages": [{"role": "human", "content": human_input}]}
            async for chunk in client.runs.stream(
                thread_id=thread["thread_id"],
                assistant_id="agent",  # Name of assistant. Defined in langgraph.json.
                input=prompt,
            ):
                print(chunk)
        except:
            import traceback

            print(traceback.format_exc())
            break


asyncio.run(main())
