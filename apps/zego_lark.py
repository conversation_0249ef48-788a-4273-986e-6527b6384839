import signal
import time

from src.common.init_log import init_log

from src.lark.zlark import zlark

init_log()


def signal_handler(signum, frame):
    """处理退出信号"""
    print("\n收到退出信号，正在停止...")
    zlark.stop_wss()
    exit(0)


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 初始化Lark
    zlark.init()

    print("Lark 客户端已启动，按 Ctrl+C 退出...")

    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止...")
        zlark.stop_wss()
