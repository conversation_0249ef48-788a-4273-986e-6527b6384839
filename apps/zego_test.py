import asyncio
import logging
import os
import uuid

from langchain_core.messages import AIMessage, HumanMessage

from src import zego_graph

from src.common.init_log import init_log

init_log()


thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"
config = {"configurable": {"thread_id": thread_id}}


logger = logging.getLogger(__name__)


async def main():
    async def stream_graph_updates(user_input: str):
        input = {"messages": [{"role": "user", "content": user_input}]}
        async for event in zego_graph.astream(input, config, stream_mode="values", debug=False):
            if "messages" in event:
                current_message = event["messages"][-1]  # TDOO 把自定义type抛出来，这样飞书消息就能做了
                if type(current_message) is HumanMessage:
                    pass
                elif type(current_message) is AIMessage:
                    print(f"\n")
                    print(f"### ====================================================================")
                    print(f"### {current_message.name}:")
                    print(f"### {current_message.content}")
                    print(f"### ====================================================================")
                    print(f"\n")

    # await stream_graph_updates("""看下阿联酋 966811601 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下沙特阿拉伯 3206531758 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下沙特阿拉伯 997297939 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    await stream_graph_updates("""看下 英国的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")


asyncio.run(main())
