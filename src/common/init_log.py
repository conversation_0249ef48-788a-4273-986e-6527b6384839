import logging

import os
from datetime import datetime

from src.common.utils.path_utils import get_log_root


def init_log():
    LOG_DIR = get_log_root()
    os.makedirs(LOG_DIR, exist_ok=True)

    # 定义日志文件名（带时间戳）
    LOG_FILE_NAME = f"{LOG_DIR}/{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"

    # 自定义日志配置
    logging.basicConfig(
        format="%(asctime)s [%(name)s] %(levelname)s: %(message)s",
        level=logging.INFO,
        handlers=[
            logging.FileHandler(LOG_FILE_NAME),  # 输出到文件
            logging.StreamHandler(),  # 输出到控制台
        ],
    )
