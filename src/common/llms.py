import json
import logging
import os
from pathlib import Path
from typing import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Li<PERSON>, Tu<PERSON>

from langchain.chat_models import init_chat_model
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.language_models import BaseChatModel
from src.common.models.model_list import get_llm_params

# types
LLMType = Literal["basic", "reasoning", "vision"]

# deepseek
deepseek_r1 = "deepseek-reasoner"
deepseek_r1_ppio = "deepseek-reasoner-ppio"  # 类型解析有点问题
deepseek_r1_ali = "deepseek-reasoner-ali"
deepseek_chat = "deepseek-chat"

# kimi
kimi_k2_ppio = "kimi-k2-ppio"
kimi_k2_turbo = "kimi-k2-turbo-preview"

# glm
glm_4p5_air = "glm-4.5-air"  # glm的官方api jsonmode有问题
glm_4p5 = "glm-4.5"  # glm的官方api jsonmode有问题
glm_4p5_ppio = "glm-4.5-ppio"  # ppio的封装 jsonmode没问题 (会不会是think开关的问题)

# qwen-ali
old_llm = "qwen-turbo-0919"
turbo_llm = "qwen-turbo-2025-04-28"
plus_llm = "qwen-plus-2025-04-28"

# qwen-open
qwen3_instruct_ppio = "qwen3-235b-a22b-instruct-2507-ppio"  # 有时候乱码
qwen3_thinking_ppio = "qwen3-235b-a22b-thinking-2507-ppio"  # 这个不支持json mode 有时候乱码？

qwen3_instruct = "qwen3-235b-a22b-instruct-2507"
qwen3_thinking = "qwen3-235b-a22b-thinking-2507"  # 这个不支持json mode

qwen3_instruct_30b = "qwen3-30b-a3b-instruct-2507"
qwen3_thinking_30b = "qwen3-30b-a3b-thinking-2507"  # 这个不支持json mode

# select
zego_llm = qwen3_instruct_30b

# const
AGENT_LLM_MAP: dict[str, str] = {
    # zego
    "zego_planner": zego_llm,
    "zego_steps": zego_llm,
    # default
    "default": zego_llm,
}

AGENT_LLM_PARAMS_MAP: dict[str, dict] = {
    "bs_planner": {"temperature": 1.2},
    "bs_steps": {"temperature": 1.3},
    "bs_refine": {"temperature": 1.4},
    "writer_outline": {"temperature": 1.4},
    "writer": {"temperature": 1.5},
}
# values
_llm_cache: dict[str, BaseChatModel] = {}
logger = logging.getLogger(__name__)


# funcs
def _get_cache_key(llm_name: str, params: dict[str, Any], disable_thinking: bool) -> str:
    """生成缓存键，考虑LLM名称和参数。

    Args:
        llm_name: LLM名称
        params: LLM参数

    Returns:
        缓存键
    """
    # 确保参数是有序的，以便相同参数的不同顺序生成相同的键
    sorted_params = json.dumps(params, sort_keys=True)
    return f"{llm_name}:{sorted_params}:{disable_thinking}"


def get_llm(node_name: str, params: dict[str, Any] = {}, disable_thinking: bool = False) -> BaseChatModel:
    cache_key = _get_cache_key(node_name, params, disable_thinking)

    if cache_key in _llm_cache:
        return _llm_cache[cache_key]

    if node_name not in AGENT_LLM_MAP:
        model_name = AGENT_LLM_MAP["default"]
        logger.warning(f"Using default LLM for {node_name}, model_name: {model_name}")
    else:
        model_name = AGENT_LLM_MAP[node_name]
    llm_params = get_llm_params(model_name)
    assert isinstance(llm_params, dict)
    node_params = AGENT_LLM_PARAMS_MAP.get(node_name, {})
    if disable_thinking and "enable_thinking" in llm_params["extra_body"]:
        llm_params["extra_body"].pop("enable_thinking")

    merge_params = {**llm_params, **node_params, **params}  # 如果有相同的key，后者覆盖前者

    if model_name.startswith("deepseek"):
        from langchain_deepseek import ChatDeepSeek

        merge_params.pop("model_provider")
        merge_params["api_base"] = merge_params["base_url"]
        base_chat_model = ChatDeepSeek(**merge_params)
    else:
        base_chat_model = init_chat_model(**merge_params)

    _llm_cache[cache_key] = base_chat_model
    return base_chat_model


def get_default_embedding_model():
    ali_embedding_model = DashScopeEmbeddings(
        model="text-embedding-v3",
        dashscope_api_key=os.getenv("ALI_API_KEY"),
    )
    return ali_embedding_model


def get_test_llm() -> BaseChatModel:
    llm_params = get_llm_params(turbo_llm)
    assert isinstance(llm_params, dict)
    base_chat_model = init_chat_model(**llm_params)
    return base_chat_model
