import asyncio
import inspect
import json
import logging
import os
import typing
import warnings
from abc import ABC, abstractmethod
from collections.abc import AsyncIterator, Iterator, Sequence
from functools import cached_property
from operator import itemgetter
from pathlib import Path
from typing import Any, Callable, cast, List, Literal, Optional, TYPE_CHECKING, Union

from langchain_core.messages import BaseMessage, SystemMessage

from langchain_core.runnables.utils import Output
from src.common.llms import get_llm
from src.common.utils.message_utils import MessageUtils
from src.common.utils.token_utils import TokenTools

logger = logging.getLogger(__name__)


def create_token_update(input_tokens: int, output_tokens: int) -> dict:
    """
    创建token更新字典，用于更新状态中的token统计

    Args:
        input_tokens: 输入token数量
        output_tokens: 输出token数量

    Returns:
        包含token统计的字典
    """
    return {
        "total_input_tokens": input_tokens,
        "total_output_tokens": output_tokens,
    }


async def get_response_from_astream(chunks: AsyncIterator[Output], callback=None) -> Output:
    first = True
    async for chunk in chunks:
        if first:
            response = chunk
            first = False
        else:
            response = response + chunk
        if callback:
            callback(chunk)
    return response


async def structured_request(
    node_name: str,
    input_messages: List[BaseMessage],
    schema_type: Union[typing.Dict, type],
    llm_params: dict[str, Any] = {},
    retry_index=0,
):
    approximately_input_tokens = TokenTools.count_messages_approximately_tokens(input_messages)
    logger.info(f"[common_structured_request][{node_name}] approximately_input_tokens: {approximately_input_tokens}")
    # json mode 不能用astream
    # 报错response = response + chunk
    # TypeError: unsupported operand type(s) for +: 'WriterOutlineSchema' and 'WriterOutlineSchema'
    try:
        response = (
            await get_llm(node_name, llm_params)
            .with_structured_output(schema_type, method="json_mode", include_raw=True)
            .ainvoke(input_messages)
        )
    except Exception as e:
        logger.error(f"[common_structured_request][{node_name}] ❌请求失败ainvoke,{e=}")
        try:
            response = await get_response_from_astream(
                get_llm(node_name, llm_params)
                .with_structured_output(schema_type, method="json_mode", include_raw=True)
                .astream(
                    input_messages,
                )
            )
        except Exception as e:
            logger.error(f"[common_structured_request][{node_name}] ❌请求失败astream,{e=}")
            raise e
    # 解析输出
    logger.info(f"[common_structured_request][{node_name}] ✅请求成功，type(response)={type(response)}")
    response_raw, response_parsed, response_error = MessageUtils.parse_include_raw_response(response)

    # 初始化token统计
    input_tokens = 0
    output_tokens = 0

    if response_raw.usage_metadata is not None:
        input_tokens = response_raw.usage_metadata.get("input_tokens", 0)
        output_tokens = response_raw.usage_metadata.get("output_tokens", 0)
        TokenTools.fit_to_coefficient(input_messages, input_tokens)
        logger.info(f"[common_structured_request][{node_name}] ✅ usage_metadata={response_raw.usage_metadata}")
        logger.info(
            f"[common_structured_request][{node_name}] ✅ input_tokens={input_tokens}, output_tokens={output_tokens}"
        )
    else:
        logger.warning(f"[common_structured_request][{node_name}] response 未包含 token 用量信息")

    if response_parsed is None:
        if response_error is None:
            logger.error(
                f"[common_structured_request][{node_name}] ❌ 响应解析失败, response_parsed=None, response_error=None, {len(response_raw.content)=}"
            )
        else:
            logger.error(
                f"[common_structured_request][{node_name}] ❌失败原因： {response_error=}, {len(response_raw)=}"
            )
        if retry_index < 3:
            retry_index += 1
            logger.error(f"[common_structured_request][{node_name}] ❌重试，正在第 {retry_index}/3 次重试")
            return await structured_request(node_name, input_messages, schema_type, retry_index=retry_index)
        else:
            logger.error(f"[common_structured_request][{node_name}] ❌请求失败： {response['raw'].content=}")
            raise response_error

    # 返回token统计信息
    return response_raw, response_parsed, response_error, input_tokens, output_tokens
