import typing
from typing import Optional, Union

from langchain_core.messages import AIMessage


class MessageUtils:
    @staticmethod
    def parse_include_raw_response(
        response: dict,
    ) -> tuple[
        AIMessage,
        Optional[Union[typing.Dict, type]],
        Optional[BaseException],
    ]:
        response_raw: AIMessage = response["raw"]
        response_parsed: Optional[Union[typing.Dict, type]] = response.get("parsed", None)
        response_error: Optional[BaseException] = response.get("error", None)
        return response_raw, response_parsed, response_error
