from typing import List

import numpy as np
from langchain_core.messages import BaseMessage
from langchain_core.messages.utils import count_tokens_approximately


class TokenTools:
    chars_per_token = 4.0

    # 近似一个系数
    approximately_input_tokens: List[int] = []
    actual_input_tokens: List[int] = []
    k: float = 2.0  # 中文初始系数设置为2倍

    @classmethod
    def count_messages_approximately_tokens(cls, input_messages: List[BaseMessage]) -> int:
        return int(cls.k * count_tokens_approximately(input_messages))

    @classmethod
    def fit_to_coefficient(cls, input_messages: List[BaseMessage], actual_input_token: int) -> int:
        if actual_input_token is None or actual_input_token < 1:
            return
        approximately_input_token = count_tokens_approximately(input_messages)
        cls.approximately_input_tokens.append(approximately_input_token)
        cls.actual_input_tokens.append(actual_input_token)
        cls.k = sum(cls.actual_input_tokens) / sum(cls.approximately_input_tokens)
