"""Define a custom Reasoning and Action agent.

Works with a chat model with tool calling support.
"""

from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph import StateGraph
from langgraph.store.memory import InMemoryStore

from src.nodes.aggregator.aggregator_node import aggregator_node
from src.nodes.dimension_analysis.dimension_analysis_nodes import (
    appid_trend_analysis_node,
    country_trend_analysis_node,
    error_distribution_analysis_node,
    isp_trend_analysis_node,
    metric_trend_analysis_node,
    sdk_trend_analysis_node,
)
from src.nodes.dispatcher.dispatcher_node import dispatcher_router, zego_dispatcher_node

# 节点导入（已删除 issue_init）

from src.state import State


zego_graph = (
    StateGraph(State)
    # 并行分析架构
    # 添加所有节点
    .set_entry_point("zego_dispatcher")
    .add_node("zego_dispatcher", zego_dispatcher_node)
    # 并行维度分析节点
    .add_node("metric_trend_analysis", metric_trend_analysis_node)
    .add_node("error_distribution_analysis", error_distribution_analysis_node)
    .add_node("sdk_trend_analysis", sdk_trend_analysis_node)
    .add_node("country_trend_analysis", country_trend_analysis_node)
    .add_node("isp_trend_analysis", isp_trend_analysis_node)
    .add_node("appid_trend_analysis", appid_trend_analysis_node)
    # 聚合节点
    .add_node("aggregator", aggregator_node)
    # 设置边
    # 使用conditional edge处理并行路由
    .add_conditional_edges("zego_dispatcher", dispatcher_router)
    # 并行维度分析节点完成后都会自动路由到aggregator
    .add_edge("metric_trend_analysis", "aggregator")
    .add_edge("error_distribution_analysis", "aggregator")
    .add_edge("sdk_trend_analysis", "aggregator")
    .add_edge("country_trend_analysis", "aggregator")
    .add_edge("isp_trend_analysis", "aggregator")
    .add_edge("appid_trend_analysis", "aggregator")
    # 设置结束点
    .set_finish_point("aggregator")
    # 编译图，包含Store配置
    .compile(
        name="zego_team",
        store=InMemoryStore(),
        # checkpointer=InMemorySaver(),  # 可选：添加checkpointer以支持持久化
    )
)

"""
并行分析架构：

1. zego_dispatcher: 问题分析和调度节点，识别参数，决定启动哪些维度的并行分析，并预先查询数据
2. 并行维度分析节点：
   - metric_trend_analysis: 指标趋势分析
   - error_distribution_analysis: 错误码分布分析 
   - sdk_trend_analysis: SDK版本趋势分析
   - country_trend_analysis: 国家维度趋势分析
   - isp_trend_analysis: 运营商趋势分析
   - appid_trend_analysis: 客户维度趋势分析
3. aggregator: 聚合所有维度的分析结果，生成最终结论

"""
