def card_element_markdown(markdown_body: str):
    return {
        "tag": "markdown",
        "content": markdown_body,
        "text_align": "left",
        "text_size": "normal_v2",
        "margin": "0px 0px 0px 0px",
    }


def card_element_zhedie(title: str, elements: list):
    return {
        "tag": "collapsible_panel",  # 折叠面板的标签。
        "element_id": "custom_id",  # 操作组件的唯一标识。JSON 2.0 新增属性。用于在调用组件相关接口中指定组件。需开发者自定义。
        "direction": "vertical",  # 面板内组件的排列方向。JSON 2.0 新增属性。可选值："vertical"（垂直排列）、"horizontal"（水平排列）。默认为 "vertical"。
        "vertical_spacing": "8px",  # 面板内组件的垂直间距。JSON 2.0 新增属性。可选值："small"(4px)、"medium"(8px)、"large"(12px)、"extra_large"(16px)或[0,99]px。
        "horizontal_spacing": "8px",  # 面板内组件内的垂直间距。JSON 2.0 新增属性。可选值："small"(4px)、"medium"(8px)、"large"(12px)、"extra_large"(16px)或[0,99]px。
        "vertical_align": "top",  # 面板内组件的垂直居中方式。JSON 2.0 新增属性。默认值为 top。
        "horizontal_align": "left",  # 面板内组件的水平居中方式。JSON 2.0 新增属性。默认值为 left。
        "padding": "8px 8px 8px 8px",  # 折叠面板的内边距。JSON 2.0 新增属性。支持范围 [0,99]px。
        "margin": "0px 0px 0px 0px",  # 折叠面板的外边距。JSON 2.0 新增属性。默认值 "0"，支持范围 [-99,99]px。
        "expanded": False,  # 面板是否展开。默认值 false。
        "background_color": "grey",  # 折叠面板的背景色，默认为透明。
        "header": {
            # 折叠面板的标题设置。
            "title": {
                # 标题文本设置。支持 plain_text 和 markdown。
                "tag": "markdown",
                "content": title,
            },
            "background_color": "grey",  # 标题区的背景色，默认为透明。
            "vertical_align": "center",  # 标题区的垂直居中方式。
            "padding": "4px 0px 4px 8px",  # 标题区的内边距。
            "position": "top",  # 标题区的位置。
            "width": "auto",  # 标题区的宽度。默认值为 fill。
            "icon": {
                # 标题前缀图标
                "tag": "standard_icon",  # 图标类型.
                "token": "down-small-ccm_outlined",  # 图标库中图标的 token。当 tag 为 standard_icon 时生效。
                "color": "orange",  # 图标的颜色。当 tag 为 standard_icon 时生效。
                # "img_key": "img_v2_38811724",  # 自定义前缀图标的图片 key。当 tag 为 custom_icon 时生效。
                "size": "16px 16px",  # 图标的尺寸。默认值为 10px 10px。
            },
            "icon_position": "follow_text",  # 图标的位置。默认值为 right。
            "icon_expanded_angle": -180,  # 折叠面板展开时图标旋转的角度，正值为顺时针，负值为逆时针。默认值为 180。
        },
        "border": {
            # 边框设置。默认不显示边框。
            "color": "grey",  # 边框的颜色。
            "corner_radius": "5px",  # 圆角设置。
        },
        "elements": elements,  # 此处可添加各个组件的 JSON 结构。暂不支持表单（form）组件。
    }
