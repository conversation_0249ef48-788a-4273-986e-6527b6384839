import json as json

import lark_oapi as lark


class LarkTask:
    lark_msg: lark.im.v1.P2ImMessageReceiveV1
    content: str

    def __init__(self, lark_msg: lark.im.v1.P2ImMessageReceiveV1):
        self.lark_msg = lark_msg
        if self.valid():
            json_str = self.lark_msg.event.message.content
            json_obj = json.loads(json_str)
            text = json_obj.get("text")
            self.content = text.replace("@_user_1 /check", "").strip()
        else:
            self.content = None

    def valid(self) -> bool:
        return "@_user_1 /check" in self.lark_msg.event.message.content


__all__ = ["LarkTask"]
