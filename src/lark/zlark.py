import asyncio
import json
import logging
import os
import threading
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

import lark_oapi as lark

from dotenv import load_dotenv
from src.lark.card.elements import card_element_markdown
from src.lark.task import process_lark_message

print(f"loading env from {os.path.join(os.path.dirname(__file__), '.env')}")
load_dotenv(os.path.join(os.path.dirname(__file__), ".env"))
assert os.getenv("LARK_APP_ID") is not None
assert os.getenv("LARK_APP_SECRET") is not None
import lark_oapi as lark
from lark_oapi.api.im.v1 import *


class ZLark:
    """ZLark Lark核心管理类"""

    def __init__(self):
        """初始化ZLark管理器"""
        self._client = None
        self._wss = None
        self.wss_thread = None
        self.client_status = {
            "is_initialized": False,
            "last_initialized": None,
            "app_id": os.getenv("LARK_APP_ID"),
            "error_msg": None,
        }
        self.wss_status = {
            "is_connected": False,
            "is_thread_running": False,
            "last_connected": None,
            "error_msg": None,
        }

    def get_client_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return self.client_status.copy()

    def get_wss_status(self) -> Dict[str, Any]:
        """获取WebSocket状态"""
        return self.wss_status.copy()

    def _update_client_status(self, is_initialized: bool, error_msg: Optional[str] = None):
        """更新客户端状态"""
        self.client_status.update(
            {
                "is_initialized": is_initialized,
                "last_initialized": datetime.now() if is_initialized else self.client_status["last_initialized"],
                "error_msg": error_msg,
            }
        )

    def _update_wss_status(
        self, is_connected: bool = None, is_thread_running: bool = None, error_msg: Optional[str] = None
    ):
        """更新WebSocket状态"""
        updates = {"error_msg": error_msg}
        if is_connected is not None:
            updates["is_connected"] = is_connected
            if is_connected:
                updates["last_connected"] = datetime.now()
        if is_thread_running is not None:
            updates["is_thread_running"] = is_thread_running
        self.wss_status.update(updates)

    @property
    def client(self):
        """获取Lark客户端实例（延迟初始化）"""
        if self._client is None:
            try:
                # see https://open.feishu.cn/document/server-side-sdk/python--sdk/invoke-server-api
                self._client = (
                    lark.Client.builder()
                    .app_id(os.getenv("LARK_APP_ID"))
                    .app_secret(os.getenv("LARK_APP_SECRET"))
                    .domain(lark.FEISHU_DOMAIN)
                    .timeout(3)
                    .app_type(lark.AppType.SELF)
                    .log_level(lark.LogLevel.DEBUG)
                    .build()
                )
                self._update_client_status(True)
                print("Lark客户端初始化成功!")
            except Exception as e:
                error_msg = f"Lark客户端初始化失败: {str(e)}"
                self._update_client_status(False, error_msg)
                print(error_msg)
                return None
        return self._client

    @property
    def wss(self):
        """获取Lark WebSocket客户端实例（延迟初始化）"""
        if self._wss is None:
            try:
                # 注册回调
                event_handler = (
                    lark.EventDispatcherHandler.builder("", "")
                    .register_p2_im_message_receive_v1(self._do_p2_im_message_receive_v1)
                    .build()
                )
                print(f"{os.getenv('LARK_APP_ID')=}, {os.getenv('LARK_APP_SECRET')=}")
                self._wss = lark.ws.Client(
                    os.getenv("LARK_APP_ID"),
                    os.getenv("LARK_APP_SECRET"),
                    event_handler=event_handler,
                    log_level=lark.LogLevel.DEBUG,
                )
                print("Lark WebSocket客户端初始化成功!")
            except Exception as e:
                error_msg = f"Lark WebSocket客户端初始化失败: {str(e)}"
                self._update_wss_status(error_msg=error_msg)
                print(error_msg)
                return None
        return self._wss

    def _do_p2_im_message_receive_v1(self, lark_msg: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """处理接收到的消息"""
        print(f"[ do_p2_im_message_receive_v1 access ], data: {lark.JSON.marshal(lark_msg, indent=4)}")
        # 在已存在的事件循环中创建任务，而不是使用 asyncio.run()
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(process_lark_message(lark_msg, self))
        except RuntimeError:
            # 如果没有运行中的事件循环，则使用 asyncio.run()
            asyncio.run(process_lark_message(lark_msg, self))

    def _start_wss_thread(self):
        """在单独线程中启动WebSocket连接"""
        if not self.wss:
            return
        try:
            print("启动 Lark WebSocket 连接...")
            self._update_wss_status(is_connected=True)
            self.wss.start()
        except Exception as e:
            error_msg = f"Lark WebSocket 连接出错: {e}"
            self._update_wss_status(is_connected=False, error_msg=error_msg)
            print(error_msg)

    def start_wss(self):
        """启动WebSocket连接"""
        if self.wss_thread is not None and self.wss_thread.is_alive():
            print("WebSocket线程已在运行中")
            return

        # 确保WebSocket客户端已初始化
        if not self.wss:
            return

        # 在单独的线程中启动WebSocket连接
        self.wss_thread = threading.Thread(target=self._start_wss_thread, daemon=True)
        self.wss_thread.start()
        self._update_wss_status(is_thread_running=True)
        print("Lark WebSocket 线程已启动")

    def stop_wss(self):
        """停止Lark WebSocket连接"""
        if self._wss:
            try:
                # self._wss.stop() # 'Client' object has no attribute 'stop'
                self._update_wss_status(is_connected=False)
                print("Lark WebSocket 连接已停止")
            except Exception as e:
                error_msg = f"停止 Lark WebSocket 连接时出错: {e}"
                self._update_wss_status(error_msg=error_msg)
                print(error_msg)

        if self.wss_thread and self.wss_thread.is_alive():
            self.wss_thread.join(timeout=5)  # 等待最多5秒
            if self.wss_thread.is_alive():
                print("警告: WebSocket 线程未能在5秒内停止")
            else:
                self._update_wss_status(is_thread_running=False)

    def init(self):
        """初始化所有组件"""
        self.client  # 触发延迟初始化
        self.start_wss()
        lark.logger.setLevel(logging.WARNING)

    def test_connection(self) -> bool:
        """测试Lark连接"""
        try:
            if self.client:
                # 这里可以添加具体的测试逻辑，比如调用一个简单的API
                print("Lark连接测试成功!")
                return True
            else:
                print("Lark连接测试失败!")
                return False
        except Exception as e:
            error_text = f"Lark连接测试失败: {str(e)}"
            print(error_text)
            return False

    # "user_id": "nemoyuan",
    # "open_id": "ou_2512b283847a64e0d8293d5339b76708",
    # "union_id": "on_4546482bcfa2739b6be2e9077ad50814"
    # "message_id": "om_x100b47aab0a66d240f1c9034b3f3da2",
    # "create_time": "1753870310867",
    # "update_time": "1753870310867",
    # "chat_id": "oc_393d1ff6d2cda476197e87e31ad8f864",
    #
    # open_id：标识一个用户在某个应用中的身份。同一个用户在不同应用中的 Open ID 不同。了解更多：如何获取 Open ID
    # union_id：标识一个用户在某个应用开发商下的身份。同一用户在同一开发商下的应用中的 Union ID 是相同的，在不同开发商下的应用中的 Union ID 是不同的。通过 Union ID，应用开发商可以把同个用户在多个应用中的身份关联起来。了解更多：如何获取 Union ID？
    # user_id：标识一个用户在某个租户内的身份。同一个用户在租户 A 和租户 B 内的 User ID 是不同的。在同一个租户内，一个用户的 User ID 在所有应用（包括商店应用）中都保持一致。User ID 主要用于在不同的应用间打通用户数据。了解更多：如何获取 User ID？
    # email：以用户的真实邮箱来标识用户。
    # chat_id：以群 ID 来标识群聊。了解更多：

    def send_msg(self, content: str, receive_id: str, receive_id_type: str = "chat_id"):
        content = json.dumps({"text": content})

        # 构造请求对象
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id(receive_id)
                .msg_type("text")
                .content(content)
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .receive_id_type(receive_id_type)
            .build()
        )

        # 发起请求
        response: CreateMessageResponse = self.client.im.v1.message.create(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))

    def reply_msg(self, content: str, message_id: str, reply_in_thread: bool = True):
        content = json.dumps({"text": content})

        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(content)
                .msg_type("text")
                .reply_in_thread(reply_in_thread)
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .message_id(message_id)
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.reply failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))

    def reply_card_msg(self, title, subtitle, markdown_body: str, message_id: str, reply_in_thread: bool = True):
        content = json.dumps(
            {
                "schema": "2.0",
                "config": {
                    "update_multi": True,
                    "style": {"text_size": {"normal_v2": {"default": "normal", "pc": "normal", "mobile": "heading"}}},
                },
                "header": {
                    "title": {"tag": "plain_text", "content": title},
                    "subtitle": {"tag": "plain_text", "content": subtitle},
                    "template": "blue",
                    "padding": "12px 12px 12px 12px",
                },
                "body": {
                    "direction": "vertical",
                    "padding": "12px 12px 12px 12px",
                    "elements": [card_element_markdown(markdown_body)],
                },
            }
        )
        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(content)
                .msg_type("interactive")
                .reply_in_thread(reply_in_thread)
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .message_id(message_id)
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.reply failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))

    def reply_custom_card_msg(self, title, subtitle, elements: list, message_id: str, reply_in_thread: bool = True):
        content = json.dumps(
            {
                "schema": "2.0",
                "config": {
                    "update_multi": True,
                    "style": {"text_size": {"normal_v2": {"default": "normal", "pc": "normal", "mobile": "heading"}}},
                },
                "header": {
                    "title": {"tag": "plain_text", "content": title},
                    "subtitle": {"tag": "plain_text", "content": subtitle},
                    "template": "blue",
                    "padding": "12px 12px 12px 12px",
                },
                "body": {
                    "direction": "vertical",
                    "padding": "12px 12px 12px 12px",
                    "elements": elements,
                },
            }
        )
        # 构造请求对象
        request: ReplyMessageRequest = (
            ReplyMessageRequest.builder()
            .request_body(
                ReplyMessageRequestBody.builder()
                .content(content)
                .msg_type("interactive")
                .reply_in_thread(reply_in_thread)
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .message_id(message_id)
            .build()
        )

        # 发起请求
        response: ReplyMessageResponse = self.client.im.v1.message.reply(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.im.v1.message.reply failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}"
            )
            return

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))


# 全局zlark实例
zlark = ZLark()


if __name__ == "__main__":
    # zlark.reply_card_msg(
    #     title="测试title",
    #     subtitle="测试subtitle",
    #     markdown_body="**Hello**\n\nThis is a **markdown** message.",
    #     message_id="om_x100b4797ca038c700ecfe2e3a439cc6",
    # )
    from card import card_element_zhedie

    zlark.reply_custom_card_msg(
        title="测试title",
        subtitle="测试subtitle",
        message_id="om_x100b4797ca038c700ecfe2e3a439cc6",
        elements=[card_element_zhedie("折叠标题", [card_element_markdown("折叠内容")])],
    )
