import logging
from typing import Literal

from langchain_core.messages import AIMessage, SystemMessage
from langgraph.types import Command

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.aggregator.aggregator_prompt import aggregator_prompt_template, json_prompt_template
from src.nodes.common.dimension_config import DimensionManager
from src.nodes.common.types import AggregatorInfo
from src.state import State

logger = logging.getLogger(__name__)


async def aggregator_node(state: State) -> Command[Literal["__end__"]]:
    """
    聚合节点：收集所有维度分析的结果，生成最终的综合分析结论
    """
    logger.info("🤖aggregator node is working.")
    node_name = "aggregator"
    state_update = {"messages": []}

    # 检查是否有维度分析结果
    dimension_analysis_results = state.get("dimension_analysis_results", {})

    # 如果没有任何分析结果，直接结束
    if len(dimension_analysis_results) == 0:
        # 创建默认的聚合分析结果
        default_aggregator_info = AggregatorInfo(
            thinking="未检测到任何维度分析结果，无法进行综合分析。",
            final_conclusion="由于没有任何维度分析数据，分析过程结束。",
            has_critical_issues=False,
            confidence_level="低",
        )
        state_update["messages"].append(default_aggregator_info.to_message())
        state_update.update({"aggregator_info": default_aggregator_info})
        return Command(update=state_update, goto="__end__")

    # 构建提示词
    prompt_list = [
        aggregator_prompt_template.format(CURRENT_TIME=get_prompt_timestamp()),
        json_prompt_template.format(json_schema=AggregatorInfo.model_json_schema()),
    ]
    prompt = "\n".join(prompt_list)

    # 输入消息 - 只使用用户问题、dispatcher分析和各维度结论消息
    input_messages = [*state.get("messages", []), SystemMessage(content=prompt)]

    # 请求LLM
    response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
        node_name, input_messages, AggregatorInfo
    )
    state_update.update(create_token_update(input_tokens, output_tokens))

    # 处理响应
    aggregator_info: AggregatorInfo = response_parsed
    logger.info(
        f"[{node_name}] ✅aggregator_info = \n{aggregator_info.model_dump_json(indent=4, exclude_none=False)}\n"
    )

    # 使用统一的消息生成方法
    state_update["messages"].append(aggregator_info.to_message())

    # 更新状态
    state_update.update({"aggregator_info": aggregator_info})

    return Command(update=state_update, goto="__end__")
