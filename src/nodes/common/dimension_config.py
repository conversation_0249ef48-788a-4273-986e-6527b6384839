"""
维度分析配置管理器
统一管理所有维度的元数据，避免硬编码和不一致问题
"""

from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass
class DimensionConfig:
    """单个维度的配置信息"""

    # 字段名（用于 DispatcherInfo 中的布尔字段）
    field_name: str

    # 维度键（用于数据存储、节点名称等）
    dimension_key: str

    # 中文显示名称
    display_name: str

    # 查询类型描述（必须匹配 DataQueryParams 的 metric_type Literal 值）
    query_description: str

    # 状态字段前缀（用于生成 {dimension}_done, {dimension}_found_issues 等字段）
    state_prefix: str


class DimensionManager:
    """维度管理器：统一管理所有维度配置"""

    # 所有维度的配置定义
    _DIMENSIONS = [
        DimensionConfig(
            field_name="need_trend_analysis",
            dimension_key="metric_trend_analysis",
            display_name="指标趋势分析",
            query_description="指标趋势分析",  # 匹配更新后的 DataQueryParams analysis_type
            state_prefix="metric_trend",
        ),
        DimensionConfig(
            field_name="need_error_analysis",
            dimension_key="error_distribution_analysis",
            display_name="错误码趋势分析",
            query_description="指标趋势分析",  # 错误码分布通过metric_type="错误码分布"区分
            state_prefix="error_distribution",
        ),
        DimensionConfig(
            field_name="need_sdk_analysis",
            dimension_key="sdk_trend_analysis",
            display_name="SDK版本趋势分析",
            query_description="SDK版本趋势分析",  # 匹配更新后的 DataQueryParams analysis_type
            state_prefix="sdk_trend",
        ),
        DimensionConfig(
            field_name="need_country_analysis",
            dimension_key="country_trend_analysis",
            display_name="国家维度趋势分析",
            query_description="国家维度趋势分析",  # 匹配更新后的 DataQueryParams analysis_type
            state_prefix="country_trend",
        ),
        DimensionConfig(
            field_name="need_isp_analysis",
            dimension_key="isp_trend_analysis",
            display_name="运营商趋势分析",
            query_description="运营商趋势分析",  # 匹配更新后的 DataQueryParams analysis_type
            state_prefix="isp_trend",
        ),
        DimensionConfig(
            field_name="need_appid_analysis",
            dimension_key="appid_trend_analysis",
            display_name="客户维度趋势分析",
            query_description="客户维度趋势分析",  # 匹配更新后的 DataQueryParams analysis_type
            state_prefix="appid_trend",
        ),
    ]

    # 创建各种映射字典
    _field_to_config: Dict[str, DimensionConfig] = {d.field_name: d for d in _DIMENSIONS}
    _key_to_config: Dict[str, DimensionConfig] = {d.dimension_key: d for d in _DIMENSIONS}
    _state_prefix_to_config: Dict[str, DimensionConfig] = {d.state_prefix: d for d in _DIMENSIONS}

    @classmethod
    def get_all_dimensions(cls) -> List[DimensionConfig]:
        """获取所有维度配置"""
        return cls._DIMENSIONS.copy()

    @classmethod
    def get_dimension_by_field(cls, field_name: str) -> Optional[DimensionConfig]:
        """根据字段名获取维度配置"""
        return cls._field_to_config.get(field_name)

    @classmethod
    def get_dimension_by_key(cls, dimension_key: str) -> Optional[DimensionConfig]:
        """根据维度键获取维度配置"""
        return cls._key_to_config.get(dimension_key)

    @classmethod
    def get_dimension_by_state_prefix(cls, state_prefix: str) -> Optional[DimensionConfig]:
        """根据状态前缀获取维度配置"""
        return cls._state_prefix_to_config.get(state_prefix)

    @classmethod
    def get_dimension_query_mapping(cls) -> Dict[str, str]:
        """获取维度键到查询描述的映射（兼容旧的 DIMENSION_QUERY_MAPPING）"""
        return {d.dimension_key: d.query_description for d in cls._DIMENSIONS}

    @classmethod
    def get_display_name_mapping(cls) -> Dict[str, str]:
        """获取维度键到显示名称的映射"""
        return {d.dimension_key: d.display_name for d in cls._DIMENSIONS}

    @classmethod
    def get_field_name_mapping(cls) -> Dict[str, str]:
        """获取字段名到维度键的映射"""
        return {d.field_name: d.dimension_key for d in cls._DIMENSIONS}

    @classmethod
    def validate_field_name(cls, field_name: str) -> bool:
        """验证字段名是否有效"""
        return field_name in cls._field_to_config

    @classmethod
    def validate_dimension_key(cls, dimension_key: str) -> bool:
        """验证维度键是否有效"""
        return dimension_key in cls._key_to_config

    @classmethod
    def get_all_dimension_keys(cls) -> List[str]:
        """获取所有维度键"""
        return [d.dimension_key for d in cls._DIMENSIONS]

    @classmethod
    def get_all_field_names(cls) -> List[str]:
        """获取所有字段名"""
        return [d.field_name for d in cls._DIMENSIONS]


# 常用的常量定义（用于数据中心等模块）
INITIAL_DATA_KEY = "initial"

# 动态生成维度键常量
for _dim in DimensionManager.get_all_dimensions():
    # 为每个维度创建常量，如 METRIC_TREND_KEY = "metric_trend_analysis"
    _const_name = f"{_dim.state_prefix.upper().replace('_', '_')}_KEY"
    globals()[_const_name] = _dim.dimension_key

# 删除临时变量
del _dim, _const_name
