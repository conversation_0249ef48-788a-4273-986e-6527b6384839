from datetime import datetime, timedelta
from typing import List, Optional

import pandas as pd

from langchain_core.load.serializable import Serializable
from langchain_core.messages import AIMessage
from pydantic import Field
from src.zego_tools import DataQueryParams

from .dimension_config import DimensionManager

_current_lc_namespace = ["src", "teams", "zego_team", "common", "types"]


class DataResult:
    title: str
    sql: str
    result_df: pd.DataFrame

    def __init__(self, title: str, sql: str, result_df: pd.DataFrame):
        self.title = title
        self.sql = sql
        self.result_df = result_df


class DispatcherInfo(Serializable):
    thinking: str = Field(..., description="对问题的分析思考")
    query_params_list: List[DataQueryParams] = Field(
        ...,
        description="需要执行的数据查询参数列表。每个查询参数包含指标类型、分析类型、时间范围和过滤条件。根据用户问题生成所需的查询组合，可以包含多种指标、分析维度的组合。",
    )

    def to_active_dimensions(self) -> list[str]:
        """
        根据查询参数列表，生成需要激活的维度列表
        """
        active_dimensions = []

        # 维度映射：从 analysis_type 到 dimension_key
        analysis_to_dimension = {
            "指标趋势分析": "metric_trend_analysis",
            "SDK版本趋势分析": "sdk_trend_analysis",
            "国家维度趋势分析": "country_trend_analysis",
            "运营商趋势分析": "isp_trend_analysis",
            "客户维度趋势分析": "appid_trend_analysis",
        }

        for query_param in self.query_params_list:
            # 如果是错误码分布分析，映射到错误分布维度
            if query_param.metric_type == "错误码分布":
                if "error_distribution_analysis" not in active_dimensions:
                    active_dimensions.append("error_distribution_analysis")
            # 其他分析类型根据 analysis_type 映射
            elif query_param.analysis_type in analysis_to_dimension:
                dimension_key = analysis_to_dimension[query_param.analysis_type]
                if dimension_key not in active_dimensions:
                    active_dimensions.append(dimension_key)

        return active_dimensions

    def to_message(self) -> AIMessage:
        """生成调度分析消息"""
        # 从查询参数列表中提取分析信息
        if not self.query_params_list:
            dimensions_text = "无"
            queries_text = "无查询任务"
        else:
            # 收集所有要进行的分析类型
            analysis_types = set()
            for query_param in self.query_params_list:
                if query_param.metric_type == "错误码分布":
                    analysis_types.add("错误码分布分析")
                else:
                    analysis_types.add(query_param.analysis_type)

            dimensions_text = "、".join(sorted(analysis_types))

            # 生成查询任务列表
            queries_text = "\n".join(
                [
                    f"  • {query_param.metric_name} - {query_param.metric_type} - {query_param.analysis_type}"
                    f" ({query_param.time_start} 至 {query_param.time_end})"
                    for query_param in self.query_params_list
                ]
            )

        ai_message = AIMessage(name="dispatcher", content="")
        ai_message.additional_kwargs = {
            "thinking": self.thinking,
        }
        ai_message.content = f"""🤖 **问题分析和调度计划**

**将进行的分析维度：**
{dimensions_text}

**具体查询任务：**
{queries_text}

"""

        return ai_message

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        """Get the namespace of the langchain object."""
        return _current_lc_namespace


class DimensionAnalysisInfo(Serializable):
    thinking: str = Field(..., description="该维度的分析思考")
    dimension_name: str = Field(..., description="维度名称")
    analysis_conclusion: str = Field(..., description="该维度的分析结论")
    found_issues: bool = Field(..., description="是否发现问题")
    issue_details: Optional[str] = Field(default=None, description="问题详情描述")
    confidence_level: str = Field(default="高", description="分析结论的置信度：高/中/低")

    def to_message(self) -> AIMessage:
        """生成维度分析消息"""
        issue_status = "是" if self.found_issues else "否"

        ai_message = AIMessage(name=f"{self.dimension_name}", content="")
        ai_message.additional_kwargs = {
            "thinking": self.thinking,
        }
        ai_message.content = f"""🔍 **{self.dimension_name}分析结果**

**发现问题：** {issue_status}
**置信度：** {self.confidence_level}

**分析结论：**
{self.analysis_conclusion}
"""

        if self.found_issues and self.issue_details:
            ai_message.content += f"""

**问题详情：**
{self.issue_details}"""

        return ai_message

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        """Get the namespace of the langchain object."""
        return _current_lc_namespace


class AggregatorInfo(Serializable):
    thinking: str = Field(..., description="对所有维度分析结果的综合思考")
    final_conclusion: str = Field(..., description="最终的综合分析结论")
    has_critical_issues: bool = Field(..., description="是否发现关键问题")
    critical_issues: str = Field(default=None, description="关键问题的详细描述")
    confidence_level: str = Field(default="高", description="结论的置信度：高/中/低")

    def to_message(self) -> AIMessage:
        """生成聚合分析消息"""
        ai_message = AIMessage(name="多维度分析汇总", content="")
        ai_message.additional_kwargs = {
            "thinking": self.thinking,
        }
        ai_message.content = f"""🎯 **最终综合分析结论**

**最终结论：**
{self.final_conclusion}

**发现关键问题：** {'是' if self.has_critical_issues else '否'}
**置信度：** {self.confidence_level}"""

        if self.has_critical_issues and self.critical_issues:
            ai_message.content += f"""

⚠️ **关键问题详情：**
{self.critical_issues}"""

        return ai_message

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        """Get the namespace of the langchain object."""
        return _current_lc_namespace


# IssueInitInfo 已合并到 DispatcherInfo 中，此类型已删除


APP_SERIALIZABLE_MAPPINGS = {
    ("src", "teams", "zego_team", "common", "types", "DispatcherInfo"): (
        "src",
        "teams",
        "zego_team",
        "common",
        "types",
        "DispatcherInfo",
    ),
    ("src", "teams", "zego_team", "common", "types", "DimensionAnalysisInfo"): (
        "src",
        "teams",
        "zego_team",
        "common",
        "types",
        "DimensionAnalysisInfo",
    ),
    ("src", "teams", "zego_team", "common", "types", "AggregatorInfo"): (
        "src",
        "teams",
        "zego_team",
        "common",
        "types",
        "AggregatorInfo",
    ),
    # IssueInitInfo 序列化映射已删除
}
