import logging
from typing import Literal

from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.common.data_center import DataCenter
from src.nodes.common.types import DimensionAnalysisInfo
from src.nodes.dimension_analysis.prompts import json_prompt_template
from src.state import State

logger = logging.getLogger(__name__)


class BaseDimensionNode:
    """
    维度分析节点的基类，封装所有重复逻辑
    """

    def __init__(self, dimension_key: str, dimension_name: str, prompt_template: str):
        self.dimension_key = dimension_key
        self.dimension_name = dimension_name
        self.prompt_template = prompt_template
        self.logger = logging.getLogger(f"{__name__}.{dimension_key}")

    async def execute(
        self, state: State, config: RunnableConfig, *, store: BaseStore
    ) -> Command[Literal["aggregator"]]:
        """
        执行维度分析的统一逻辑
        """
        self.logger.info(f"🤖{self.dimension_key} node is working.")
        state_update = {"messages": []}

        # 使用DataCenter获取数据
        data_entry = DataCenter.get_data(store, config, self.dimension_key)

        # 初始化token计数变量
        input_tokens = 0
        output_tokens = 0

        # 检查数据状态
        if not data_entry or data_entry.get("data") is None or data_entry.get("data").empty:
            self.logger.warning(f"[{self.dimension_key}] 未找到预查询的数据")
            analysis_info = self._create_default_analysis_info("未获取到有效数据")
        else:
            data_df = data_entry.get("data")
            data_rows = len(data_df)

            # 检查数据量是否超过3000行
            if data_rows > 3000:
                self.logger.warning(f"[{self.dimension_key}] 数据量过多({data_rows}行)，超过3000行限制")
                analysis_info = self._create_default_analysis_info("数据过多请检查")
            else:
                # 获取数据提示词
                data_prompt = DataCenter.get_data_prompt(
                    store, config, self.dimension_key, f"{self.dimension_name}数据"
                )

                prompt_list = [
                    self.prompt_template.format(CURRENT_TIME=get_prompt_timestamp()),
                    json_prompt_template.format(json_schema=DimensionAnalysisInfo.model_json_schema()),
                    data_prompt,
                ]
                prompt = "\n".join(prompt_list)

                history_messages = state.get("messages", [])
                input_messages = [*history_messages, SystemMessage(content=prompt)]

                # 请求LLM
                response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
                    self.dimension_key, input_messages, DimensionAnalysisInfo
                )
                state_update.update(create_token_update(input_tokens, output_tokens))
                analysis_info: DimensionAnalysisInfo = response_parsed

        self.logger.info(
            f"[{self.dimension_key}] ✅analysis_info = \n{analysis_info.model_dump_json(indent=4, exclude_none=False)}\n"
        )

        # 使用统一的消息生成方法
        dimension_message = analysis_info.to_message()
        dimension_message.additional_kwargs = {
            "input_tokens": input_tokens,
            "output_tokens": state_update,
            "thinking": analysis_info.thinking,
        }

        # 更新状态 - 使用统一的字典结构存储分析结果
        state_update["messages"].append(dimension_message)

        # 初始化dimension_analysis_results如果不存在
        current_results = state.get("dimension_analysis_results", {})
        current_results[self.dimension_key] = analysis_info
        state_update["dimension_analysis_results"] = current_results

        return Command(update=state_update, goto="aggregator")

    def _create_default_analysis_info(self, error_type: str = "未获取到有效数据") -> DimensionAnalysisInfo:
        """创建默认分析结果（当无数据或数据异常时）"""
        if error_type == "数据过多请检查":
            thinking = f"{self.dimension_name}数据量超过3000行，为保证分析质量和系统性能，请检查数据范围或调整查询条件"
            conclusion = f"由于{self.dimension_name}数据量过多（>3000行），请检查数据范围"
        else:
            thinking = f"未获取到有效的{self.dimension_name}数据，无法进行深度分析"
            conclusion = f"由于数据获取异常，无法完成{self.dimension_name}分析"

        return DimensionAnalysisInfo(
            thinking=thinking,
            dimension_name=self.dimension_name,
            analysis_conclusion=conclusion,
            found_issues=False,
            confidence_level="低",
        )
