from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command
from src.nodes.common.dimension_config import DimensionManager

from src.nodes.dimension_analysis.base_dimension_node import BaseDimensionNode
from src.nodes.dimension_analysis.prompts import (
    appid_trend_prompt_template,
    country_trend_prompt_template,
    error_distribution_prompt_template,
    isp_trend_prompt_template,
    metric_trend_prompt_template,
    sdk_trend_prompt_template,
)
from src.state import State


async def metric_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """指标趋势分析节点 - 统一处理指标趋势和错误码趋势"""
    dimension_config = DimensionManager.get_dimension_by_key("metric_trend_analysis")

    # 根据metric_type选择对应的prompt模板
    if hasattr(state, "query_params") and state.query_params.metric_type == "错误码分布":
        prompt_template = error_distribution_prompt_template
        dimension_name = "错误码趋势分析"
    else:
        prompt_template = metric_trend_prompt_template
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=prompt_template,
    ).execute(state, config, store=store)


async def error_distribution_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """错误码分布分析节点 - 已合并到metric_trend_analysis_node中"""
    # 重定向到metric_trend_analysis_node
    return await metric_trend_analysis_node(state, config, store=store)


async def sdk_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """SDK版本趋势分析节点 - 统一处理SDK维度趋势和下钻分析"""
    dimension_config = DimensionManager.get_dimension_by_key("sdk_trend_analysis")

    # 根据metric_type选择对应的prompt模板和显示名称
    if hasattr(state, "query_params") and state.query_params.metric_type == "错误码分布":
        dimension_name = f"SDK版本错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=sdk_trend_prompt_template,
    ).execute(state, config, store=store)


async def country_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """国家维度趋势分析节点 - 统一处理国家维度趋势和下钻分析"""
    dimension_config = DimensionManager.get_dimension_by_key("country_trend_analysis")

    # 根据metric_type选择对应的显示名称
    if hasattr(state, "query_params") and state.query_params.metric_type == "错误码分布":
        dimension_name = f"国家维度错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=country_trend_prompt_template,
    ).execute(state, config, store=store)


async def isp_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """运营商趋势分析节点 - 统一处理运营商维度趋势和下钻分析"""
    dimension_config = DimensionManager.get_dimension_by_key("isp_trend_analysis")

    # 根据metric_type选择对应的显示名称
    if hasattr(state, "query_params") and state.query_params.metric_type == "错误码分布":
        dimension_name = f"运营商维度错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=isp_trend_prompt_template,
    ).execute(state, config, store=store)


async def appid_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """客户维度趋势分析节点 - 统一处理客户维度趋势和下钻分析"""
    dimension_config = DimensionManager.get_dimension_by_key("appid_trend_analysis")

    # 根据metric_type选择对应的显示名称
    if hasattr(state, "query_params") and state.query_params.metric_type == "错误码分布":
        dimension_name = f"客户维度错误码下钻分析"
    else:
        dimension_name = dimension_config.display_name

    return await BaseDimensionNode(
        dimension_key=dimension_config.dimension_key,
        dimension_name=dimension_name,
        prompt_template=appid_trend_prompt_template,
    ).execute(state, config, store=store)
