import logging
from typing import Dict, Literal

import pandas as pd
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Send

from src.common.utils.llm_request_utils import create_token_update, structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.common.data_center import DataCenter
from src.nodes.common.dimension_config import DimensionManager
from src.nodes.common.types import DispatcherInfo
from src.nodes.dispatcher.dispatcher_prompt import dispatcher_prompt_template, json_prompt_template
from src.state import State
from src.zego_tools import DataQueryParams, execute_sql_for_llm, generate_sql

logger = logging.getLogger(__name__)


async def zego_dispatcher_node(state: State, config: RunnableConfig, *, store: BaseStore):
    """
    问题分析和调度节点：分析用户问题，识别参数，决定需要启动哪些维度的并行分析任务，同时预先查询所需数据
    """
    logger.info("🤖zego_dispatcher node is working.")
    node_name = "zego_dispatcher"
    state_update = {"messages": []}

    # 构建提示词
    prompt_list = [
        dispatcher_prompt_template.format(
            CURRENT_TIME=get_prompt_timestamp(),
        ),
        json_prompt_template.format(
            json_schema=DispatcherInfo.model_json_schema(),
        ),
    ]
    prompt = "\n".join(prompt_list)

    # 输入消息
    history_messages = state.get("messages", [])
    input_messages = [*history_messages, SystemMessage(content=prompt)]

    # 请求LLM
    response_raw, response_parsed, response_error, input_tokens, output_tokens = await structured_request(
        node_name, input_messages, DispatcherInfo
    )
    state_update.update(create_token_update(input_tokens, output_tokens))

    # 处理响应
    dispatcher_info: DispatcherInfo = response_parsed
    logger.info(
        f"[{node_name}] ✅dispatcher_info = \n{dispatcher_info.model_dump_json(indent=4, exclude_none=False)}\n"
    )

    # 使用统一的消息生成方法
    state_update["messages"].append(dispatcher_info.to_message())

    # 批量执行数据查询
    active_dimensions = dispatcher_info.to_active_dimensions()
    logger.info(f"[{node_name}] 开始批量查询数据，维度: {active_dimensions}")
    await _batch_query_data(dispatcher_info, node_name, store, config)

    # 返回更新的状态，包含所有必要信息（数据已通过DataCenter存储）
    state_update.update({"dispatcher_info": dispatcher_info})

    return state_update


async def _batch_query_data(
    dispatcher_info: DispatcherInfo, node_name: str, store: BaseStore, config: RunnableConfig
) -> None:
    """
    批量查询各维度需要的数据，直接使用 DispatcherInfo 中的 query_params_list
    存储结构：{"dimension": {"data": DataFrame, "error_info": str, "query_params": DataQueryParams}}
    """
    active_dimensions = dispatcher_info.to_active_dimensions()

    # 创建维度到查询参数的映射
    dimension_to_queries = {}
    analysis_to_dimension = {
        "指标趋势分析": "metric_trend_analysis",
        "SDK版本趋势分析": "sdk_trend_analysis",
        "国家维度趋势分析": "country_trend_analysis",
        "运营商趋势分析": "isp_trend_analysis",
        "客户维度趋势分析": "appid_trend_analysis",
    }

    for query_param in dispatcher_info.query_params_list:
        # 确定维度键
        if query_param.metric_type == "错误码分布":
            dimension_key = "error_distribution_analysis"
        elif query_param.analysis_type in analysis_to_dimension:
            dimension_key = analysis_to_dimension[query_param.analysis_type]
        else:
            logger.warning(f"[{node_name}] 未知分析类型: {query_param.analysis_type}")
            continue

        if dimension_key not in dimension_to_queries:
            dimension_to_queries[dimension_key] = []
        dimension_to_queries[dimension_key].append(query_param)

    # 为每个维度执行查询（使用第一个查询参数）
    for dimension in active_dimensions:
        if dimension not in dimension_to_queries:
            logger.warning(f"[{node_name}] 维度 {dimension} 没有对应的查询参数")
            continue

        # 使用该维度的第一个查询参数
        query_params = dimension_to_queries[dimension][0]

        try:
            # 直接使用 query_params，无需转换
            sql = generate_sql(query_params)
            result, error_info_text = await execute_sql_for_llm(sql)

            # 使用DataCenter存储数据到LangGraph Store
            DataCenter.store_data(
                store=store,
                config=config,
                data_key=dimension,
                data=result,
                error_info=error_info_text,
                query_params=query_params,
                query_title=f"{query_params.analysis_type}分析数据",
            )

            logger.info(f"[{node_name}] ✅ {dimension} 查询完成: {len(result)} 条记录")
            if "错误码说明" in error_info_text:
                logger.info(f"[{node_name}] ✅ {dimension} 包含错误码信息")

        except Exception as e:
            logger.error(f"[{node_name}] ❌ {dimension} 查询失败: {e}")
            # 存储空数据和错误信息
            DataCenter.store_data(
                store=store,
                config=config,
                data_key=dimension,
                data=pd.DataFrame(),
                error_info=f"查询失败: {str(e)}",
                query_params=query_params,
                query_title=f"{query_params.analysis_type}分析数据（查询失败）",
            )


def dispatcher_router(state: State):
    """
    根据dispatcher的决策结果，决定并行执行哪些维度分析节点
    """
    dispatcher_info = state.get("dispatcher_info")
    if not dispatcher_info:
        return "aggregator"

    active_dimensions = dispatcher_info.to_active_dimensions()

    # 如果没有任何维度需要分析，直接跳转到聚合节点
    if not active_dimensions:
        return "aggregator"

    # 返回需要并行执行的节点Send列表
    parallel_sends = []
    for dimension in active_dimensions:
        parallel_sends.append(Send(dimension, state))

    return parallel_sends
