"""Define the state structures for the agent."""

from __future__ import annotations

import base64

import lzma
import operator

from pathlib import Path
from typing import Dict, Optional, TypedDict

import pandas as pd

from langchain_core.load.dump import dumps
from langchain_core.load.load import loads
from langchain_core.messages import AnyMessage
from langchain_core.utils._merge import merge_dicts, merge_obj
from langgraph.graph import add_messages
from typing_extensions import Annotated
from src.nodes.common.types import *
from src.nodes.common.dimension_config import DimensionManager


class ConfigSchema(TypedDict):
    workspace_path: Path


class State(TypedDict):
    """State for the agent system, extends MessagesState with next field."""

    messages: Annotated[list[AnyMessage], add_messages]
    total_input_tokens: Annotated[int, operator.add]
    total_output_tokens: Annotated[int, operator.add]

    # dispatcher 分析结果（包含所有分析参数和决策信息）
    dispatcher_info: Optional[DispatcherInfo]

    dimension_analysis_results: Annotated[Dict[str, DimensionAnalysisInfo], merge_obj]

    aggregator_info: Optional[AggregatorInfo]

    @classmethod
    def deserialize_state(self, serialize_content: str) -> dict:
        # 检查是压缩b64还是原始json
        if serialize_content.startswith("B"):
            compressed_content = base64.b64decode(serialize_content[1:])
            decompressed_content = lzma.decompress(compressed_content).decode("utf-8")
            dump_content = decompressed_content
        else:
            dump_content = serialize_content[1:] if serialize_content.startswith("J") else serialize_content

        # 使用 LangChain 的 loads 进行反序列化，添加自定义命名空间
        state = loads(
            dump_content,
            valid_namespaces=["src"],
            additional_import_mappings=APP_SERIALIZABLE_MAPPINGS,
        )
        return state

    @classmethod
    def serialize_state(self, state: State) -> str:
        # 使用 LangChain 的 dumps 进行序列化
        dump_content = dumps(state)
        # lzma压缩
        compressed_content = lzma.compress(dump_content.encode("utf-8"), preset=9)
        # db需要str类型，做b64处理
        base64_content = base64.b64encode(compressed_content).decode("utf-8")

        # 如果压缩后更长则直接使用原始json
        if len(base64_content) < len(dump_content):
            serialize_content = "B" + base64_content
        else:
            serialize_content = "J" + dump_content
        return serialize_content
