"""ZEGO工具包

重构后的ZEGO工具包，提供SQL生成和执行的分离式架构：
- sql_generator: 专门负责SQL语句生成
- sql_executor: 专门负责SQL执行和结果处理
- config: 配置管理
- utils: 通用工具函数
"""

# SQL执行相关
from .sql_executor import execute_sql, execute_sql_for_llm, execute_sql_with_error_info, format_error_code_info_for_llm

# SQL生成相关
from .sql_generator import DataQueryParams, generate_sql

__all__ = [
    # 参数和生成
    "DataQueryParams",
    "generate_sql",
    # SQL执行
    "execute_sql",
    "execute_sql_with_error_info",
    "execute_sql_for_llm",
    # 错误处理
    "format_error_code_info_for_llm",
]
