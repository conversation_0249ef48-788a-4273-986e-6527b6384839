"""SQL执行工具包

这个包负责执行SQL查询，处理数据库连接、错误处理等。
与SQL生成功能完全分离，专注于SQL的执行和结果处理。
"""

from .error_handler import extract_error_code_info, format_error_code_info_for_llm
from .executor import execute_sql, execute_sql_for_llm, execute_sql_with_error_info

__all__ = [
    "execute_sql",
    "execute_sql_with_error_info",
    "execute_sql_for_llm",
    "extract_error_code_info",
    "format_error_code_info_for_llm",
]
