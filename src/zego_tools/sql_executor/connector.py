"""
异步数据库连接器模块，负责管理与StarRocks数据库的异步连接和查询
"""

import logging
import os
import socket
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, Optional

import aiomysql
import pandas as pd
import socks
from dotenv import load_dotenv

load_dotenv(os.path.join(os.path.dirname(__file__), ".env"), override=True)

logger = logging.getLogger(__name__)


class AsyncDatabaseConnection:
    """异步数据库连接管理类"""

    def __init__(self):
        """初始化连接管理器"""
        self.connection = None
        self.connection_pool = None
        self.connection_status = {
            "is_connected": False,
            "last_connected": None,
            "host": os.getenv("ZEGO_MYSQL_URL"),
            "port": os.getenv("ZEGO_MYSQL_PORT", "9030"),
            "database": os.getenv("ZEGO_MYSQL_DB"),
            "user": os.getenv("ZEGO_MYSQL_USER"),
            "error_msg": None,
        }
        logger.info(f"{self.connection_status}")

    def get_status(self) -> Dict[str, Any]:
        """获取当前连接状态"""
        return self.connection_status.copy()

    def _update_status(self, is_connected: bool, error_msg: Optional[str] = None):
        """更新连接状态"""
        self.connection_status.update(
            {
                "is_connected": is_connected,
                "last_connected": datetime.now() if is_connected else self.connection_status["last_connected"],
                "error_msg": error_msg,
            }
        )

    @asynccontextmanager
    async def _proxy_context(self):
        """代理上下文管理器，确保代理只在数据库操作时生效"""
        original_socket = socket.socket
        proxy_enabled = False

        try:
            # 获取代理配置
            proxy_host = os.getenv("ZEGO_SOCKS_PROXY")
            proxy_port = os.getenv("ZEGO_SOCKS_PORT")
            proxy_user = os.getenv("ZEGO_SOCKS_PROXY_USER")
            proxy_password = os.getenv("ZEGO_SOCKS_PROXY_PASSWORD")

            if proxy_host and proxy_port:
                # 设置SOCKS代理
                socks.set_default_proxy(
                    socks.SOCKS5, proxy_host, int(proxy_port), username=proxy_user, password=proxy_password, rdns=True
                )
                socket.socket = socks.socksocket
                proxy_enabled = True
                logger.debug(f"已启用SOCKS5代理")

            yield proxy_enabled

        except Exception as e:
            logger.error(f"代理设置失败: {str(e)}")
            yield False

        finally:
            # 恢复原始socket，确保不影响其他网络请求
            if proxy_enabled:
                socket.socket = original_socket
                # 清除全局代理设置
                socks.set_default_proxy(None)
                logger.debug("SOCKS代理已关闭")

    async def get_connection(self) -> Optional[aiomysql.Connection]:
        """获取数据库连接"""
        # 检查现有连接是否可用
        if self.connection is not None:
            try:
                await self.connection.ping()
                return self.connection
            except Exception:
                logger.warning("现有连接已失效，尝试重新连接...")
                await self._close_connection()

        # 在代理上下文中创建新连接
        async with self._proxy_context() as proxy_enabled:
            try:
                conn = await aiomysql.connect(
                    host=os.getenv("ZEGO_MYSQL_URL"),
                    port=int(os.getenv("ZEGO_MYSQL_PORT", "9030")),
                    user=os.getenv("ZEGO_MYSQL_USER"),
                    password=os.getenv("ZEGO_MYSQL_PASSWORD"),
                    db=os.getenv("ZEGO_MYSQL_DB"),
                    charset="utf8mb4",
                    connect_timeout=30,
                )

                self.connection = conn
                self._update_status(True)
                logger.debug("数据库连接成功!")
                return conn

            except Exception as e:
                error_msg = f"数据库连接失败: {str(e)}"
                self._update_status(False, error_msg)
                logger.error(error_msg)
                return None

    async def _close_connection(self):
        """关闭数据库连接"""
        if self.connection is not None:
            try:
                self.connection.close()
            except Exception as e:
                logger.error(f"关闭连接时出错: {str(e)}")
            finally:
                self.connection = None

        self._update_status(False)

    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            # 在代理上下文中测试连接
            async with self._proxy_context():
                conn = await self.get_connection()
                if conn:
                    async with conn.cursor() as cursor:
                        await cursor.execute("SELECT 1")
                        await cursor.fetchone()
                    logger.debug("数据库连接测试成功!")
                    return True
                else:
                    logger.error("数据库连接测试失败!")
                    return False
        except Exception as e:
            error_text = f"数据库连接测试失败: {str(e)}"
            logger.error(error_text)
            return False

    async def execute_query(self, sql: str) -> Optional[pd.DataFrame]:
        """执行SQL查询并返回DataFrame"""
        start_time = datetime.now()

        try:
            # 在代理上下文中执行查询
            async with self._proxy_context():
                conn = await self.get_connection()
                if not conn:
                    logger.error("无法获取数据库连接")
                    return None

                async with conn.cursor() as cursor:
                    await cursor.execute(sql)
                    columns = [desc[0] for desc in cursor.description]
                    rows = await cursor.fetchall()

                    # 转换为DataFrame
                    results = pd.DataFrame(rows, columns=columns)

                # 计算查询时间
                query_time = (datetime.now() - start_time).total_seconds()

                logger.info(f"查询完成，返回 {len(results)} 条记录，耗时 {query_time:.2f} 秒")

                return results

        except Exception as e:
            error_text = f"查询执行失败: {str(e)}"
            logger.error(error_text)
            return None

    async def close(self):
        """关闭连接"""
        await self._close_connection()


# 全局异步数据库连接实例（延迟初始化）
_async_db_connection = None


def get_async_db_connection() -> AsyncDatabaseConnection:
    """获取全局异步数据库连接实例（延迟初始化）"""
    global _async_db_connection
    if _async_db_connection is None:
        _async_db_connection = AsyncDatabaseConnection()
    return _async_db_connection


# 为了保持向后兼容，创建一个属性访问器
class _AsyncConnectionProxy:
    def __getattr__(self, name):
        return getattr(get_async_db_connection(), name)


async_db_connection = _AsyncConnectionProxy()
