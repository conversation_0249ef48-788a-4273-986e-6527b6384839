"""错误处理模块

负责处理SQL执行过程中的错误码解析和格式化。
"""

from typing import Optional

import pandas as pd

from src.zego_tools.error_codes import error_code_map


def extract_error_code_info(df: pd.DataFrame) -> Optional[pd.DataFrame]:
    """
    从DataFrame中提取错误码信息并返回错误码说明

    Args:
        df: 查询结果DataFrame

    Returns:
        包含错误码说明的DataFrame，如果没有错误码列则返回None
    """
    if df is None or df.empty:
        return None

    # 检查是否有错误码相关列
    error_code_columns = []
    for col in df.columns:
        if "error_code" in col.lower() or col.lower() in ["错误码", "error"]:
            error_code_columns.append(col)

    if not error_code_columns:
        return None

    # 提取所有唯一的错误码
    unique_error_codes = set()
    for col in error_code_columns:
        if df[col].dtype in ["int64", "int32", "float64", "float32"]:
            # 数值类型的错误码
            codes = df[col].dropna().unique()
            unique_error_codes.update([int(code) for code in codes if pd.notna(code)])
        else:
            # 字符串类型的错误码，尝试转换为数字
            codes = df[col].dropna().unique()
            for code in codes:
                try:
                    unique_error_codes.add(int(code))
                except (ValueError, TypeError):
                    continue

    if not unique_error_codes:
        return None

    # 构建错误码说明DataFrame
    error_info_data = []
    for error_code in sorted(unique_error_codes):
        description = error_code_map.get(error_code, "未知错误码")
        error_info_data.append({"error_code": error_code, "description": description})

    return pd.DataFrame(error_info_data)


def format_error_code_info_for_llm(error_info_df: Optional[pd.DataFrame]) -> str:
    """
    将错误码信息格式化为适合传递给大模型的字符串

    Args:
        error_info_df: 错误码信息DataFrame

    Returns:
        格式化后的错误码信息字符串
    """
    if error_info_df is None or error_info_df.empty:
        return "本次查询结果中未包含错误码信息。"

    info_lines = ["## 错误码说明"]
    info_lines.append("以下是查询结果中出现的错误码及其详细说明：")
    info_lines.append("")

    for _, row in error_info_df.iterrows():
        error_code = row["error_code"]
        description = row["description"]
        info_lines.append(f"**错误码 {error_code}**: {description}")
        info_lines.append("")

    return "\n".join(info_lines)
