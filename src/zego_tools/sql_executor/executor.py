"""SQL执行器核心模块

负责异步执行SQL查询并处理结果。
"""

from typing import Optional, Tuple, Union

import pandas as pd

from .connector import async_db_connection
from .error_handler import extract_error_code_info, format_error_code_info_for_llm


async def execute_sql(
    sql: str, include_error_info: bool = True
) -> Union[pd.DataFrame, Tuple[pd.DataFrame, Optional[pd.DataFrame]]]:
    """
    异步执行SQL语句

    Args:
        sql: SQL查询语句
        include_error_info: 是否包含错误码信息，默认为True

    Returns:
        如果include_error_info为False，返回查询结果DataFrame
        如果include_error_info为True，返回元组(查询结果DataFrame, 错误码信息DataFrame)
    """
    result = await async_db_connection.execute_query(sql)

    if not include_error_info:
        return result

    # 提取错误码信息
    error_info = extract_error_code_info(result)

    return result, error_info


async def execute_sql_with_error_info(sql: str) -> <PERSON><PERSON>[pd.DataFrame, Optional[pd.DataFrame]]:
    """
    异步执行SQL查询并返回结果和错误码信息

    Args:
        sql: SQL查询语句

    Returns:
        元组(查询结果DataFrame, 错误码信息DataFrame)
    """
    return await execute_sql(sql, include_error_info=True)


async def execute_sql_for_llm(sql: str) -> Tuple[pd.DataFrame, str]:
    """
    为大模型相关请求异步执行SQL查询，返回数据和格式化的错误码信息

    Args:
        sql: SQL查询语句

    Returns:
        元组(查询结果DataFrame, 格式化的错误码信息字符串)
    """
    result, error_info = await execute_sql_with_error_info(sql)
    error_info_text = format_error_code_info_for_llm(error_info)

    return result, error_info_text
