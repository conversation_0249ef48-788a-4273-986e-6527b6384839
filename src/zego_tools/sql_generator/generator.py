"""重构后的SQL生成器核心模块

专门负责根据查询参数生成对应的SQL查询语句。
"""

from datetime import datetime

from .metrics import metrics_registry, TableGranularityManager, ZEGOMetric
from .params import DataQueryParams

# 删除旧的templates依赖，现在所有SQL构建逻辑都在本文件中


def generate_sql(data_query_params: DataQueryParams) -> str:
    """
    使用查询参数生成对应的SQL语句

    Args:
        data_query_params: 数据查询参数对象

    Returns:
        生成的SQL查询语句
    """
    start_time = datetime.strptime(data_query_params.time_start, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(data_query_params.time_end, "%Y-%m-%d %H:%M:%S")

    # 根据时间范围确定数据粒度
    granularity = TableGranularityManager.auto_select_granularity(start_time, end_time)

    # 构建过滤条件
    filters = {}
    if data_query_params.appid_filter:
        filters["app_id"] = data_query_params.appid_filter
    if data_query_params.country_filter:
        filters["country"] = data_query_params.country_filter

    # 获取指标配置
    metric = metrics_registry.get(data_query_params.metric_name)
    if not metric:
        raise ValueError(f"未找到指标配置: {data_query_params.metric_name}")

    # 根据新的参数结构生成对应的SQL
    # metric_type: "默认指标" 或 "错误码分布"
    # analysis_type: "指标趋势分析", "SDK版本趋势分析", "国家维度趋势分析", "运营商趋势分析", "客户维度趋势分析"

    if data_query_params.analysis_type == "指标趋势分析":
        # 指标趋势分析
        if data_query_params.metric_type == "错误码分布":
            # 错误码分布分析
            if not metric.supports_error_distribution:
                raise ValueError(f"指标 {data_query_params.metric_name} 不支持错误码分布分析")

            return build_error_distribution_query(
                metric=metric,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
            )
        else:
            # 默认指标趋势分析
            return build_trend_query(
                metric=metric,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
            )
    elif data_query_params.analysis_type in (
        "SDK版本趋势分析",
        "国家维度趋势分析",
        "运营商趋势分析",
        "客户维度趋势分析",
    ):
        # 维度趋势分析
        dimension_mapping = {
            "国家维度趋势分析": "country",
            "SDK版本趋势分析": "sdk_version",
            "运营商趋势分析": "isp",
            "客户维度趋势分析": "app_id",
        }

        dimension = dimension_mapping[data_query_params.analysis_type]

        if data_query_params.metric_type == "错误码分布":
            # 错误码分布的维度下钻分析
            if not metric.supports_error_distribution:
                raise ValueError(f"指标 {data_query_params.metric_name} 不支持错误码分布分析")

            return build_error_distribution_with_dimension_query(
                metric=metric,
                dimension=dimension,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
            )
        else:
            # 默认指标的维度下钻分析
            return build_dimension_trend_query(
                metric=metric,
                primary_dimension="app_id" if data_query_params.appid_filter is not None else "country",
                primary_dimension_value=(
                    data_query_params.appid_filter
                    if data_query_params.appid_filter is not None
                    else data_query_params.country_filter
                ),
                secondary_dimension=dimension,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
            )
    else:
        raise ValueError(f"不支持的分析类型: {data_query_params.analysis_type}")


# 统一的SQL构建辅助函数
def _build_where_conditions(metric, start_time, end_time, filters):
    """构建WHERE条件"""
    where_conditions = [
        f"{metric.time_field} BETWEEN '{start_time.strftime('%Y-%m-%d %H:%M:%S')}' AND '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'"
    ]

    # 添加指标固有的过滤条件
    if metric.where_filters:
        where_conditions.append(metric.where_filters)

    # 添加用户过滤条件
    for field, value in filters.items():
        where_conditions.append(f"{field} = '{value}'")

    return where_conditions


def build_trend_query(metric, start_time, end_time, filters, granularity) -> str:
    """统一的趋势查询构建函数"""
    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters)

    sql = f"""
SELECT 
    {metric.time_field},
    {metric.value_sql} as metric_value,
    SUM({metric.sample_ct_field}) as total_count
FROM {table_name}
WHERE {' AND '.join(where_conditions)}
GROUP BY {metric.time_field}
ORDER BY {metric.time_field}
    """
    return sql


def build_error_distribution_query(metric, start_time, end_time, filters, granularity) -> str:
    """构建错误码分布查询"""
    if not metric.supports_error_distribution:
        raise ValueError(f"指标 {metric.name} 不支持错误码分布分析")

    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters)

    return metric.get_error_distribution_sql(table_name, where_conditions, granularity)


def build_error_distribution_with_dimension_query(metric, dimension, start_time, end_time, filters, granularity) -> str:
    """构建带维度下钻的错误码分布查询"""
    if not metric.supports_error_distribution:
        raise ValueError(f"指标 {metric.name} 不支持错误码分布分析")

    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters)

    return metric.get_error_distribution_with_dimension_sql(table_name, where_conditions, dimension, granularity)


def build_dimension_trend_query(
    metric, primary_dimension, primary_dimension_value, secondary_dimension, start_time, end_time, filters, granularity
) -> str:
    """构建维度趋势分析查询"""
    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters)

    if primary_dimension_value:
        where_conditions.append(f"{primary_dimension} = '{primary_dimension_value}'")

    # 使用CTE优化查询并限制维度数量
    sql = f"""
WITH daily_stats AS (
    SELECT 
        {metric.time_field},
        {secondary_dimension},
        {metric.value_sql} as metric_value,
        SUM({metric.sample_ct_field}) as total_count
    FROM {table_name}
    WHERE {' AND '.join(where_conditions)}
    GROUP BY {metric.time_field}, {secondary_dimension}
),
top_dimensions AS (
    SELECT 
        {secondary_dimension},
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY {secondary_dimension}
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT 
    ds.{metric.time_field},
    ds.{secondary_dimension},
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.{secondary_dimension} = td.{secondary_dimension}
ORDER BY ds.{metric.time_field}, td.dimension_total DESC
    """
    return sql
