"""
重构后的指标配置模块，简化设计，提高可维护性
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from src.zego_tools.sql_generator.utils import TableGranularityManager


class ZEGOMetric(ABC):
    """统一的指标类 - 简化版本"""

    def __init__(
        self,
        name: str,
        base_table: str,  # 基础表名，默认天粒度
        time_field: str,
        value_sql: str,  # 指标值计算SQL表达式
        sample_ct_field: str,  # 分母字段，用于计算total_count
        dimensions: List[str] = None,
        where_filters: str = "",  # WHERE子句过滤条件，可以包含filters和exclude_codes逻辑
        supports_error_distribution: bool = False,  # 是否支持错误码分布分析
        error_field: str = "",  # 错误字段名，仅用于错误码分布分析
    ):
        self.name = name
        self.base_table = base_table
        self.time_field = time_field
        self.value_sql = value_sql
        self.sample_ct_field = sample_ct_field
        self.dimensions = dimensions or []
        self.where_filters = where_filters
        self.supports_error_distribution = supports_error_distribution
        self.error_field = error_field

    def get_table_name(self, granularity: str = "1day") -> str:
        """获取指定粒度的表名"""
        return TableGranularityManager.get_table_name(self.base_table, granularity)

    def get_error_distribution_sql(
        self, table_name: str, where_conditions: List[str], granularity: str = "1day"
    ) -> str:
        """
        生成错误码分布分析的SQL

        Args:
            table_name: 表名
            where_conditions: WHERE条件列表
            granularity: 时间粒度

        Returns:
            错误码分布分析SQL
        """
        if not self.supports_error_distribution:
            raise ValueError(f"指标 {self.name} 不支持错误码分布分析")

        where_clause = " AND ".join(where_conditions)
        if self.where_filters:
            where_clause += f" AND {self.where_filters}"

        sql = f"""
SELECT 
    {self.error_field} as error_code,
    SUM({self.sample_ct_field}) as error_count,
    ROUND(SUM({self.sample_ct_field}) * 100.0 / (
        SELECT SUM({self.sample_ct_field}) 
        FROM {table_name} 
        WHERE {where_clause}
    ), 2) as error_rate
FROM {table_name}
WHERE {where_clause}
GROUP BY {self.error_field}
ORDER BY error_count DESC
LIMIT 20
        """
        return sql

    def get_error_distribution_with_dimension_sql(
        self, table_name: str, where_conditions: List[str], dimension: str, granularity: str = "1day"
    ) -> str:
        """
        生成带维度下钻的错误码分布分析SQL

        Args:
            table_name: 表名
            where_conditions: WHERE条件列表
            dimension: 下钻维度（如country, sdk_version等）
            granularity: 时间粒度

        Returns:
            带维度下钻的错误码分布分析SQL
        """
        if not self.supports_error_distribution:
            raise ValueError(f"指标 {self.name} 不支持错误码分布分析")

        where_clause = " AND ".join(where_conditions)
        if self.where_filters:
            where_clause += f" AND {self.where_filters}"

        sql = f"""
WITH error_stats AS (
    SELECT 
        {self.error_field} as error_code,
        {dimension},
        SUM({self.sample_ct_field}) as error_count
    FROM {table_name}
    WHERE {where_clause}
    GROUP BY {self.error_field}, {dimension}
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.{dimension},
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC
        """
        return sql


# 删除了CustomValueMetric类，现在使用统一的Metric类


# 指标配置注册表
class MetricsRegistry:
    """指标注册表"""

    def __init__(self):
        self._metrics: Dict[str, ZEGOMetric] = {}

    def register(self, metric: ZEGOMetric):
        """注册指标"""
        self._metrics[metric.name] = metric

    def get(self, name: str) -> Optional[ZEGOMetric]:
        """获取指标"""
        return self._metrics.get(name)

    def list_all(self) -> Dict[str, ZEGOMetric]:
        """获取所有指标"""
        return self._metrics.copy()


# 创建全局注册表实例
metrics_registry = MetricsRegistry()


# 注册具体的指标配置
def register_default_metrics():
    """注册默认的指标配置 - 简化版本"""

    # 公共维度列表
    common_dimensions = ["platform", "country", "region", "city", "isp", "src", "sdk_version", "os_type", "app_id"]

    # 错误码维度（包含error字段）
    error_dimensions = common_dimensions + ["error"]

    # 基础表名
    base_event_table = (
        "ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d"
    )
    base_play_report_table = "ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d"
    base_publish_report_table = "ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d"
    base_keysucrate_table = "ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d"
    base_fft_table = (
        "ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d"
    )
    base_consumed_table = "ocean.realtime_ads_speedlog_consumed_app_platform_country_1d"

    # 注册成功率指标
    metrics_registry.register(
        ZEGOMetric(
            name="拉流成功率",
            base_table=base_event_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=error_dimensions,
            where_filters="event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="登录成功率",
            base_table=base_event_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=error_dimensions,
            where_filters="event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="5s登录成功率",
            base_table=base_event_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=error_dimensions,
            where_filters="event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="推流成功率",
            base_table=base_event_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=error_dimensions,
            where_filters="event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    # 注册自定义数值指标
    metrics_registry.register(
        ZEGOMetric(
            name="视频卡顿率",
            base_table=base_play_report_table,
            time_field="timestamp",
            value_sql="ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4)",
            sample_ct_field="video_duration_sum",
            dimensions=common_dimensions,
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="拉流丢包率",
            base_table=base_play_report_table,
            time_field="timestamp",
            value_sql="ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4)",
            sample_ct_field="plr_cnt",
            dimensions=common_dimensions,
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="推流丢包率",
            base_table=base_publish_report_table,
            time_field="timestamp",
            value_sql="ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4)",
            sample_ct_field="plr_cnt",
            dimensions=common_dimensions,
        )
    )

    # 统一接入相关指标
    metrics_registry.register(
        ZEGOMetric(
            name="统一接入request成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error"],
            where_filters="event = 'zegoconn_request' AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="统一接入connect成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error"],
            where_filters="event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    # 3s成功率指标
    metrics_registry.register(
        ZEGOMetric(
            name="3s拉流请求成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error", "src"],
            where_filters="event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="3s推流请求成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error", "src"],
            where_filters="event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    # RTC子事件指标
    metrics_registry.register(
        ZEGOMetric(
            name="推流rtc子事件成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error", "src"],
            where_filters="event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="拉流rtc子事件成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error", "src"],
            where_filters="event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="拉流l3子事件成功率",
            base_table=base_keysucrate_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=["app_id", "country", "sdk_version", "error", "src"],
            where_filters="event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    # 调度成功率指标
    metrics_registry.register(
        ZEGOMetric(
            name="拉流调度成功率",
            base_table=base_event_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=error_dimensions,
            where_filters="event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="推流调度成功率",
            base_table=base_event_table,
            time_field="timestamp",
            value_sql="ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2)",
            sample_ct_field="err_cnt",
            dimensions=error_dimensions,
            where_filters="event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)",
            supports_error_distribution=True,
            error_field="error",
        )
    )

    # 视频首帧指标
    metrics_registry.register(
        ZEGOMetric(
            name="视频首帧",
            base_table=base_fft_table,
            time_field="timestamp",
            value_sql="ROUND(sum(fft_sum)/sum(fft_cnt),4)",
            sample_ct_field="fft_cnt",
            dimensions=common_dimensions,
            where_filters="event = 'sdk_play_decode_first_video_frame'",
        )
    )

    # 耗时相关指标
    metrics_registry.register(
        ZEGOMetric(
            name="拉流请求耗时均值",
            base_table=base_consumed_table,
            time_field="timestamp",
            value_sql="sum(play_request_num)/sum(play_request_cnt)",
            sample_ct_field="play_request_cnt",
            dimensions=["platform", "country", "app_id"],
            where_filters="coalesce(extlib_type,0) = 0",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="推流请求耗时均值",
            base_table=base_consumed_table,
            time_field="timestamp",
            value_sql="sum(pub_request_num)/sum(pub_request_cnt)",
            sample_ct_field="pub_request_cnt",
            dimensions=["platform", "country", "app_id"],
            where_filters="coalesce(extlib_type,0) = 0",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="拉流请求耗时1s占比",
            base_table=base_consumed_table,
            time_field="timestamp",
            value_sql="sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt)",
            sample_ct_field="play_request_cnt",
            dimensions=["platform", "country", "app_id"],
            where_filters="coalesce(extlib_type,0) = 0",
        )
    )

    metrics_registry.register(
        ZEGOMetric(
            name="推流请求耗时1s占比",
            base_table=base_consumed_table,
            time_field="timestamp",
            value_sql="sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt)",
            sample_ct_field="pub_request_cnt",
            dimensions=["platform", "country", "app_id"],
            where_filters="coalesce(extlib_type,0) = 0",
        )
    )

    # 端到端丢包率
    metrics_registry.register(
        ZEGOMetric(
            name="端到端丢包率",
            base_table=base_play_report_table,
            time_field="timestamp",
            value_sql="ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2)",
            sample_ct_field="peer_to_peer_plr_cnt",
            dimensions=common_dimensions,
        )
    )


# 初始化默认指标
register_default_metrics()


# 兼容性函数
def get_metric_config(metric_name: str) -> Optional[ZEGOMetric]:
    """获取指标配置（兼容原有接口）"""
    return metrics_registry.get(metric_name)
