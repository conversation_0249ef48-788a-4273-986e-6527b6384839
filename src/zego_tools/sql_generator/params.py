"""数据查询参数定义模块"""

from datetime import datetime, timedelta
from typing import Literal, Optional

from langchain_core.load.serializable import Serializable
from pydantic import Field


class DataQueryParams(Serializable):
    """数据查询参数类，定义了所有查询相关的参数和配置"""

    metric_name: Literal[
        "拉流成功率",
        "登录成功率",
        "5s登录成功率",
        "推流成功率",
        "视频卡顿率",
        "拉流丢包率",
        "推流丢包率",
        "统一接入request成功率",
        "统一接入connect成功率",
        "3s拉流请求成功率",
        "3s推流请求成功率",
        "推流rtc子事件成功率",
        "拉流rtc子事件成功率",
        "拉流l3子事件成功率",
        "拉流调度成功率",
        "推流调度成功率",
        "视频首帧",
        "拉流请求耗时均值",
        "推流请求耗时均值",
        "拉流请求耗时1s占比",
        "推流请求耗时1s占比",
        "端到端丢包率",
    ] = Field(default="拉流成功率", description="问题的指标类型")

    metric_type: Literal[
        "默认指标",  # 默认指标类型
        "错误码分布",  # 错误码分布分析（仅适用于成功率指标）
    ] = Field(default="默认指标", description="指标类型：默认指标或错误码分布")

    analysis_type: Literal[
        "指标趋势分析",  # 指标趋势分析
        "SDK版本趋势分析",  # SDK版本趋势分析
        "国家维度趋势分析",  # 国家维度趋势分析
        "运营商趋势分析",  # 运营商趋势分析
        "客户维度趋势分析",  # 客户维度趋势分析
    ] = Field(
        default="指标趋势分析",
        description="分析类型：指标趋势分析、SDK版本趋势分析、国家维度趋势分析、运营商趋势分析、客户维度趋势分析",
    )

    time_start: str = Field(
        default=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S"),
        description="数据查询的开始时间，默认30天前；注意，不同时间范围数据精度不同，7天以上数据为天粒度，7天内为小时粒度，2天内为10分钟粒度，4小时内为分钟粒度；格式为YYYY-MM-DD HH:MM:SS",
    )
    time_end: str = Field(
        default=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        description="数据查询的结束时间，默认当前时间；注意，不同时间范围数据精度不同，7天以上数据为天粒度，7天内为小时粒度，2天内为10分钟粒度，4小时内为分钟粒度；格式为YYYY-MM-DD HH:MM:SS。",
    )

    appid_filter: Optional[int] = Field(
        default=None,
        description="数据查询的客户AppID过滤条件, 如123456",
    )

    country_filter: Optional[str] = Field(
        default=None,
        description="数据查询的国家过滤条件, 如'沙特阿拉伯'，注意，country使用中文而非国家代码",
    )

    def to_prompt(self) -> str:
        """
        将查询参数转换为大模型可理解的提示词格式
        帮助大模型理解当前数据的查询条件和背景
        """
        time_range = f"{self.time_start} 至 {self.time_end}"

        prompt_parts = [
            "**数据查询参数说明**：",
            f"• 指标类型：{self.metric_name}",
            f"• 查询类型：{self.metric_type}",
            f"• 分析类型：{self.analysis_type}",
            f"• 时间范围：{time_range}",
        ]

        if self.appid_filter:
            prompt_parts.append(f"• 客户AppID过滤：{self.appid_filter}")

        if self.country_filter:
            prompt_parts.append(f"• 国家过滤：{self.country_filter}")

        # 添加查询参数的含义说明
        # 分析类型现在直接使用中文，无需映射
        analysis_desc = self.analysis_type

        prompt_parts.extend(
            [
                "",
                "**参数含义说明**：",
                f"- 本次查询针对 **{self.metric_name}** 指标进行 **{self.metric_type}** 类型的 **{analysis_desc}**",
                f"- 数据时间范围为 **{time_range}**",
            ]
        )

        if self.appid_filter:
            prompt_parts.append(f"- 数据已过滤，仅包含客户AppID为 **{self.appid_filter}** 的数据")

        if self.country_filter:
            prompt_parts.append(f"- 数据已过滤，仅包含 **{self.country_filter}** 地区的数据")
        else:
            prompt_parts.append("- 数据包含全球所有地区")

        if not self.appid_filter:
            prompt_parts.append("- 数据包含所有客户AppID")

        prompt_parts.extend(["", "**重要提醒**：分析时请基于以上查询条件来理解数据的覆盖范围和局限性。"])

        return "\n".join(prompt_parts)

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        """Get the namespace of the langchain object."""
        return ["src", "zego_tools", "sql_generator"]
