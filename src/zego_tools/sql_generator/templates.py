"""
SQL模板模块，负责生成各种指标的SQL查询
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from src.zego_tools.sql_generator.utils import get_table_granularity
from src.zego_tools.utils.field_mapper import get_db_field_name, get_display_name as get_display_name_func

from .metrics import (
    CustomValueMetric,
    get_metric_config,
    metrics_registry,
    SuccessRateMetric,
    TableGranularity,
    TableGranularityManager,
)


def get_column_name(dimension_name):
    """从显示名称获取实际列名"""
    return get_db_field_name(dimension_name)


def get_display_name(column_name):
    """从实际列名获取显示名称"""
    return get_display_name_func(column_name)


def format_date(dt: datetime) -> str:
    """格式化日期为年-月-日格式"""
    return dt.strftime("%Y-%m-%d")


def build_custom_value_query(
    metric_name: str,
    start_time: datetime,
    end_time: datetime,
    dimensions: List[str] = None,
    filters: Dict[str, Any] = None,
    table_granularity: Optional[TableGranularity] = None,
) -> str:
    """
    构建自定义数值指标查询SQL（如卡顿率等）

    Args:
        metric_name: 指标名称
        start_time: 开始时间
        end_time: 结束时间
        dimensions: 查询维度列表
        filters: 查询过滤条件
        table_granularity: 表粒度，如果为None则根据时间范围自动选择

    Returns:
        查询SQL语句
    """
    # 获取指标配置
    metric_config = get_metric_config(metric_name)
    if not metric_config:
        raise ValueError(f"未找到指标配置: {metric_name}")

    if not metric_config.is_custom_value_metric():
        raise ValueError(f"指标 {metric_name} 不是自定义数值类型指标")

    # 初始化过滤条件和维度
    dimensions = dimensions or []
    filters = filters or {}

    # 确定使用的表粒度
    if table_granularity is None:
        table_granularity = get_table_granularity(start_time, end_time)

    # 获取对应粒度的表名
    table_name = metric_config.get_table_name(table_granularity)

    # 基本参数
    time_field = metric_config.time_field
    value_sql = metric_config.value_sql
    count_field_for_custom = metric_config.count_field_for_custom

    # 构建时间过滤条件，根据粒度调整时间精度
    time_format = "%Y-%m-%d"
    if table_granularity in [
        TableGranularity.MIN_1,
        TableGranularity.MIN_10,
        TableGranularity.HOUR_1,
    ]:
        time_format = "%Y-%m-%d %H:%M:%S"

    # 构建WHERE条件
    where_conditions = [
        f"{time_field} BETWEEN '{start_time.strftime(time_format)}' AND '{end_time.strftime(time_format)}'"
    ]

    # 添加配置中的固定过滤条件
    for field, value in metric_config.filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加用户指定的过滤条件
    for field, value in filters.items():
        if field == "__custom_filter__":
            # 处理自定义过滤器
            for line in str(value).strip().split("\n"):
                line = line.strip()
                if line:  # 跳过空行
                    where_conditions.append(line)
        elif field in metric_config.dimensions:
            # 处理常规过滤器
            if isinstance(value, (list, tuple)):
                where_conditions.append(f"{field} IN ({','.join(repr(v) for v in value)})")
            else:
                where_conditions.append(f"{field} = {repr(value)}")

    where_clause = "WHERE " + " AND ".join(where_conditions)

    # 构建SELECT和GROUP BY子句
    select_dims = []
    group_by_dims = []

    # 1. 添加维度字段
    if dimensions:
        select_dims.extend(dimensions)
        group_by_dims.extend(dimensions)

    # 2. 添加时间字段，根据粒度调整
    select_dims.append(time_field)
    group_by_dims.append(time_field)

    # 3. 添加自定义数值指标字段和total_count字段
    metric_fields = [
        value_sql,
        f"SUM({count_field_for_custom}) as total_count",  # 添加total_count支持
    ]

    # 合并字段
    all_fields = select_dims + metric_fields

    # 在SQL注释中标明使用的表粒度
    granularity_comment = f"/* 使用{TableGranularity.get_display_name(table_granularity)}粒度表 */"

    return f"""
    SELECT {granularity_comment}
        {', '.join(all_fields)}
    FROM {table_name}
    {where_clause}
    GROUP BY {', '.join(group_by_dims)}
    ORDER BY {', '.join(group_by_dims)}
    """


def build_success_rate_query(
    metric_name: str,
    start_time: datetime,
    end_time: datetime,
    dimensions: List[str] = None,
    filters: Dict[str, Any] = None,
    table_granularity: Optional[TableGranularity] = None,
) -> str:
    """
    构建成功率查询SQL

    Args:
        metric_name: 指标名称
        start_time: 开始时间
        end_time: 结束时间
        dimensions: 查询维度列表
        filters: 查询过滤条件
        table_granularity: 表粒度，如果为None则根据时间范围自动选择

    Returns:
        查询SQL语句
    """
    # 获取指标配置
    metric_config = get_metric_config(metric_name)
    if not metric_config:
        raise ValueError(f"未找到指标配置: {metric_name}")

    # 检查指标类型，如果是自定义数值指标则调用对应函数
    if metric_config.is_custom_value_metric():
        return build_custom_value_query(metric_name, start_time, end_time, dimensions, filters, table_granularity)

    # 初始化过滤条件和维度
    dimensions = dimensions or []
    filters = filters or {}

    # 确定使用的表粒度
    if table_granularity is None:
        table_granularity = get_table_granularity(start_time, end_time)

    # 获取对应粒度的表名
    table_name = metric_config.get_table_name(table_granularity)

    # 基本参数
    time_field = metric_config.time_field
    count_field = metric_config.count_field
    error_field = metric_config.error_field
    success_condition = metric_config.success_condition
    success_codes_str = ",".join(str(code) for code in metric_config.success_codes)
    exclude_codes_str = ",".join(str(code) for code in metric_config.exclude_codes)

    # 构建时间过滤条件，根据粒度调整时间精度
    time_format = "%Y-%m-%d"
    if table_granularity in [
        TableGranularity.MIN_1,
        TableGranularity.MIN_10,
        TableGranularity.HOUR_1,
    ]:
        time_format = "%Y-%m-%d %H:%M:%S"

    # 构建WHERE条件
    where_conditions = [
        f"{time_field} BETWEEN '{start_time.strftime(time_format)}' AND '{end_time.strftime(time_format)}'"
    ]

    # 添加配置中的固定过滤条件
    for field, value in metric_config.filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加用户指定的过滤条件
    for field, value in filters.items():
        if field == "__custom_filter__":
            # 处理自定义过滤器
            for line in str(value).strip().split("\n"):
                line = line.strip()
                if line:  # 跳过空行
                    where_conditions.append(line)
        elif field in metric_config.dimensions:
            # 处理常规过滤器
            if isinstance(value, (list, tuple)):
                where_conditions.append(f"{field} IN ({','.join(repr(v) for v in value)})")
            else:
                where_conditions.append(f"{field} = {repr(value)}")

    where_clause = "WHERE " + " AND ".join(where_conditions)

    # 构建SELECT和GROUP BY子句
    select_dims = []
    group_by_dims = []

    # 1. 添加维度字段
    if dimensions:
        select_dims.extend(dimensions)
        group_by_dims.extend(dimensions)

    # 2. 添加时间字段，根据粒度调整
    select_dims.append(time_field)
    group_by_dims.append(time_field)

    # 3. 构建成功率公式
    success_rate_formula = f"round(sum(if({error_field} in ({success_codes_str} {f'and {success_condition}' if success_condition else ''}),{count_field},0))/sum({count_field})*100,2)"
    success_count_formula = f"SUM(if({error_field} in ({success_codes_str} {f'and {success_condition}' if success_condition else ''}),{count_field},0))"

    # 剔除黑名单后的成功率公式
    if metric_config.exclude_codes:
        exclude_rate_formula = f"""
        round(
            sum(if({error_field} in ({success_codes_str} {f'and {success_condition}' if success_condition else ''}),{count_field},0)) /
            sum(if({error_field} not in ({exclude_codes_str}),{count_field},0)) * 100,
            2
        )"""
    else:
        exclude_rate_formula = success_rate_formula

    # 4. 添加指标字段
    metric_fields = [
        f"{success_rate_formula} as success_rate",
        f"{exclude_rate_formula} as exclude_success_rate",
        f"SUM({count_field}) as total_count",
        f"{success_count_formula} as success_count",
        f"(SUM({count_field}) - {success_count_formula}) as fail_count",
        f"SUM(if({error_field} not in ({exclude_codes_str}), {count_field}, 0)) - SUM(if({error_field} in ({success_codes_str} {f'and {success_condition}' if success_condition else ''}), {count_field}, 0)) as exclude_error_count",
    ]

    # 合并字段
    all_fields = select_dims + metric_fields

    # 在SQL注释中标明使用的表粒度，但放在SELECT之后
    granularity_comment = f"/* 使用{TableGranularity.get_display_name(table_granularity)}粒度表 */"

    return f"""
    SELECT {granularity_comment}
        {', '.join(all_fields)}
    FROM {table_name}
    {where_clause}
    GROUP BY {', '.join(group_by_dims)}
    ORDER BY {', '.join(group_by_dims)}
    """


def _build_custom_value_trend_query(
    metric_config,
    dimension: str,
    dimension_value: str,
    secondary_dim: str,
    start_time: datetime,
    end_time: datetime,
    table_granularity: Optional[TableGranularity] = None,
    filters: Dict[str, Any] = None,
) -> str:
    """
    构建自定义数值指标的趋势查询SQL

    Args:
        metric_config: 指标配置对象
        dimension: 主维度名称
        dimension_value: 主维度的值
        secondary_dim: 二级维度名称
        start_time: 开始时间
        end_time: 结束时间
        table_granularity: 表粒度
        filters: 附加过滤条件

    Returns:
        str: 查询SQL
    """
    # 确定使用的表粒度
    if table_granularity is None:
        table_granularity = get_table_granularity(start_time, end_time)

    # 获取对应粒度的表名
    table_name = metric_config.get_table_name(table_granularity)

    time_field = metric_config.time_field
    value_sql = metric_config.value_sql
    count_field_for_custom = metric_config.count_field_for_custom  # 获取自定义指标的分母字段

    # 构建时间过滤条件
    time_format = "%Y-%m-%d"
    if table_granularity in [
        TableGranularity.MIN_1,
        TableGranularity.MIN_10,
        TableGranularity.HOUR_1,
    ]:
        time_format = "%Y-%m-%d %H:%M:%S"

    # 构建WHERE条件
    where_conditions = [
        f"{time_field} BETWEEN '{start_time.strftime(time_format)}' AND '{end_time.strftime(time_format)}'",
        f"{dimension} = '{dimension_value}'",
    ]

    # 添加配置中的固定过滤条件
    for field, value in metric_config.filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加自定义过滤条件
    custom_filter = filters.get("__custom_filter__") if filters else None
    if custom_filter:
        for line in str(custom_filter).strip().split("\n"):
            line = line.strip()
            if line:  # 跳过空行
                where_conditions.append(line)

    where_clause = "WHERE " + " AND ".join(where_conditions)

    granularity_comment = f"/* 使用{TableGranularity.get_display_name(table_granularity)}粒度表 */"

    # 构建SQL - 按二级维度分组统计自定义数值指标，添加total_count支持
    return f"""
    WITH {granularity_comment} daily_stats AS (
        SELECT 
            {time_field},
            {secondary_dim},
            {value_sql},
            SUM({count_field_for_custom}) as total_count
        FROM {table_name}
        {where_clause}
        GROUP BY {time_field}, {secondary_dim}
    ),
    daily_totals AS (
        SELECT 
            {time_field},
            SUM(total_count) as daily_total
        FROM 
            daily_stats
        GROUP BY 
            {time_field}
    ),
    daily_top_values AS (
        SELECT 
            {secondary_dim},
            SUM(total_count) as total
        FROM daily_stats
        GROUP BY {secondary_dim}
        ORDER BY total DESC
        LIMIT 10
    )
    SELECT 
        ds.{time_field},
        ds.{secondary_dim},
        ds.metric_value,
        ds.total_count,
        ROUND((ds.total_count / dt.daily_total) * 100, 2) as percentage_of_daily
    FROM 
        daily_stats ds
    INNER JOIN 
        daily_top_values tv ON ds.{secondary_dim} = tv.{secondary_dim}
    INNER JOIN
        daily_totals dt ON ds.{time_field} = dt.{time_field}
    ORDER BY 
        ds.{time_field}, tv.total DESC
    """


def build_error_distribution_query(
    metric_name: str,
    start_time: datetime,
    end_time: datetime,
    dimensions: List[str] = None,
    filters: Dict[str, Any] = None,
    table_granularity: Optional[TableGranularity] = None,
) -> str:
    """
    构建错误码分布查询SQL

    Args:
        metric_name: 指标名称
        start_time: 开始时间
        end_time: 结束时间
        dimensions: 查询维度列表
        filters: 查询过滤条件
        table_granularity: 表粒度，如果为None则根据时间范围自动选择

    Returns:
        查询SQL语句
    """
    # 获取指标配置
    metric_config = get_metric_config(metric_name)
    if not metric_config:
        raise ValueError(f"未找到指标配置: {metric_name}")

    # 检查指标类型，自定义数值指标不支持错误分布查询
    if metric_config.is_custom_value_metric():
        raise ValueError(f"自定义数值指标 {metric_name} 不支持错误码分布查询")

    # 初始化过滤条件和维度
    dimensions = dimensions or []
    filters = filters or {}

    # 确定使用的表粒度
    if table_granularity is None:
        table_granularity = get_table_granularity(start_time, end_time)

    # 获取对应粒度的表名
    table_name = metric_config.get_table_name(table_granularity)

    # 基本参数
    time_field = metric_config.time_field
    count_field = metric_config.count_field
    error_field = metric_config.error_field
    exclude_codes_str = ",".join(str(code) for code in metric_config.exclude_codes)

    # 构建时间过滤条件，根据粒度调整时间精度
    time_format = "%Y-%m-%d"
    if table_granularity in [
        TableGranularity.MIN_1,
        TableGranularity.MIN_10,
        TableGranularity.HOUR_1,
    ]:
        time_format = "%Y-%m-%d %H:%M:%S"

    # 构建WHERE条件
    where_conditions = [
        f"{time_field} BETWEEN '{start_time.strftime(time_format)}' AND '{end_time.strftime(time_format)}'"
    ]

    # 添加配置中的固定过滤条件
    for field, value in metric_config.filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加用户指定的过滤条件
    for field, value in filters.items():
        if field == "__custom_filter__":
            # 处理自定义过滤器
            for line in str(value).strip().split("\n"):
                line = line.strip()
                if line:  # 跳过空行
                    where_conditions.append(line)
        elif field in metric_config.dimensions:
            # 处理常规过滤器
            if isinstance(value, (list, tuple)):
                where_conditions.append(f"{field} IN ({','.join(repr(v) for v in value)})")
            else:
                where_conditions.append(f"{field} = {repr(value)}")

    where_clause = "WHERE " + " AND ".join(where_conditions)

    # 在SQL注释中标明使用的表粒度
    granularity_comment = f"/* 使用{TableGranularity.get_display_name(table_granularity)}粒度表 */"

    # 根据表粒度调整日期分组
    return f"""
    WITH {granularity_comment} error_stats AS (
        SELECT 
            {time_field},
            {error_field} as error_code,
            SUM({count_field}) as error_count,
            round(SUM({count_field}) * 100.0 / SUM(SUM({count_field})) OVER (PARTITION BY {time_field}), 2) as error_percentage,
            {error_field} IN ({exclude_codes_str} /* exclude_codes */) as is_excluded
        FROM {table_name}
        {where_clause}
        GROUP BY {time_field}, {error_field}
        ORDER BY {time_field}, error_count DESC
    )
    SELECT 
        {time_field},
        error_code,
        error_count,
        error_percentage,
        is_excluded
    FROM error_stats
    WHERE error_percentage >= 0.1
    """


def build_daily_trend_query(
    metric_name: str,
    dimension: str,
    dimension_value: str,
    secondary_dim: str,
    start_time: datetime,
    end_time: datetime,
    table_granularity: Optional[TableGranularity] = None,
    filters: Dict[str, Any] = None,
) -> str:
    """
    构建每日趋势数据查询SQL

    Args:
        metric_name: 指标名称
        dimension: 主维度名称 (如 app_id 或 country)
        dimension_value: 主维度的值
        secondary_dim: 二级维度名称 (如 sdk_version, country, app_id)
        start_time: 开始时间
        end_time: 结束时间
        table_granularity: 表粒度，如果为None则根据时间范围自动选择
        filters: 附加过滤条件

    Returns:
        str: 查询SQL
    """
    metric_config = get_metric_config(metric_name)
    if not metric_config:
        return ""

    # 检查指标类型，自定义数值指标不支持传统的趋势分析
    if metric_config.is_custom_value_metric():
        return _build_custom_value_trend_query(
            metric_config,
            dimension,
            dimension_value,
            secondary_dim,
            start_time,
            end_time,
            table_granularity,
            filters,
        )

    # 初始化过滤条件
    filters = filters or {}

    # 确定使用的表粒度
    if table_granularity is None:
        table_granularity = get_table_granularity(start_time, end_time)

    # 获取对应粒度的表名
    table_name = metric_config.get_table_name(table_granularity)

    time_field = metric_config.time_field
    count_field = metric_config.count_field
    error_field = metric_config.error_field
    success_condition = metric_config.success_condition

    # 获取成功条件
    success_codes_str = ",".join(str(code) for code in metric_config.success_codes)
    exclude_codes_str = ",".join(str(code) for code in metric_config.exclude_codes)

    # 构建时间过滤条件，根据粒度调整时间精度
    time_format = "%Y-%m-%d"
    if table_granularity in [
        TableGranularity.MIN_1,
        TableGranularity.MIN_10,
        TableGranularity.HOUR_1,
    ]:
        time_format = "%Y-%m-%d %H:%M:%S"

    # 构建WHERE条件
    where_conditions = [
        f"{time_field} BETWEEN '{start_time.strftime(time_format)}' AND '{end_time.strftime(time_format)}'",
        f"{dimension} = '{dimension_value}'",
    ]

    # 添加配置中的固定过滤条件
    for field, value in metric_config.filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加用户指定的过滤条件
    for field, value in filters.items():
        if field == "__custom_filter__":
            # 处理自定义过滤器
            for line in str(value).strip().split("\n"):
                line = line.strip()
                if line:  # 跳过空行
                    where_conditions.append(line)
        elif field in metric_config.dimensions:
            # 处理常规过滤器
            if isinstance(value, (list, tuple)):
                where_conditions.append(f"{field} IN ({','.join(repr(v) for v in value)})")
            else:
                where_conditions.append(f"{field} = {repr(value)}")

    where_clause = "WHERE " + " AND ".join(where_conditions)

    granularity_comment = f"/* 使用{TableGranularity.get_display_name(table_granularity)}粒度表 */"

    # 构建SQL - 计算每个二级维度占总请求的比例
    return f"""
    WITH {granularity_comment} daily_stats AS (
        SELECT 
            {time_field},
            {secondary_dim},
            SUM({count_field}) as total_count,
            SUM(if({error_field} in ({success_codes_str}{f' and {success_condition}' if success_condition else ''}), {count_field}, 0)) as success_count,
            SUM({count_field}) - SUM(if({error_field} in ({success_codes_str}{f' and {success_condition}' if success_condition else ''}), {count_field}, 0)) as error_count,
            SUM(if({error_field} not in ({exclude_codes_str}), {count_field}, 0)) - SUM(if({error_field} in ({success_codes_str}{f' and {success_condition}' if success_condition else ''}), {count_field}, 0)) as exclude_error_count,
            ROUND(SUM(if({error_field} in ({success_codes_str}{f' and {success_condition}' if success_condition else ''}), {count_field}, 0)) / SUM({count_field}) * 100, 2) as success_rate,
            CASE WHEN SUM(if({error_field} not in ({exclude_codes_str}), {count_field}, 0)) > 0 THEN
                ROUND(SUM(if({error_field} in ({success_codes_str}{f' and {success_condition}' if success_condition else ''}), {count_field}, 0)) / SUM(if({error_field} not in ({exclude_codes_str}), {count_field}, 0)) * 100, 2)
            ELSE
                0
            END as exclude_success_rate
        FROM {table_name}
        {where_clause}
        GROUP BY {time_field}, {secondary_dim}
    ),
    daily_totals AS (
        SELECT 
            {time_field},
            SUM(total_count) as daily_total
        FROM 
            daily_stats
        GROUP BY 
            {time_field}
    ),
    daily_top_values AS (
        SELECT 
            {secondary_dim},
            SUM(total_count) as total
        FROM daily_stats
        GROUP BY {secondary_dim}
        ORDER BY total DESC
        LIMIT 10
    )
    SELECT 
        ds.{time_field},
        ds.{secondary_dim},
        ds.total_count,
        ds.success_count,
        ds.error_count,
        ds.exclude_error_count,
        ds.success_rate,
        ds.exclude_success_rate,
        ROUND((ds.total_count / dt.daily_total) * 100, 2) as percentage_of_daily
    FROM 
        daily_stats ds
    INNER JOIN 
        daily_top_values tv ON ds.{secondary_dim} = tv.{secondary_dim}
    INNER JOIN
        daily_totals dt ON ds.{time_field} = dt.{time_field}
    ORDER BY 
        ds.{time_field}, tv.total DESC
    """


def build_excluded_dimension_query(
    metric_name: str,
    dimension: str,
    dimension_value: str,
    excluded_dim: str,
    start_time: datetime,
    end_time: datetime,
    table_granularity: Optional[TableGranularity] = None,
    filters: Dict[str, Any] = None,
) -> str:
    """
    构建剔除维度后成功率SQL

    Args:
        metric_name: 指标名称
        dimension: 主维度名称
        dimension_value: 主维度的值
        excluded_dim: 需要剔除的维度名称
        start_time: 开始时间
        end_time: 结束时间
        table_granularity: 表粒度，如果为None则根据时间范围自动选择
        filters: 附加过滤条件

    Returns:
        str: 查询SQL
    """
    metric_config = get_metric_config(metric_name)
    if not metric_config:
        return ""

    # 检查指标类型，自定义数值指标不支持传统的剔除分析
    if metric_config.is_custom_value_metric():
        return ""  # 自定义数值指标暂不支持剔除分析

    # 初始化过滤条件
    filters = filters or {}

    # 确定使用的表粒度
    if table_granularity is None:
        table_granularity = get_table_granularity(start_time, end_time)

    # 获取对应粒度的表名
    table_name = metric_config.get_table_name(table_granularity)

    time_field = metric_config.time_field
    count_field = metric_config.count_field
    error_field = metric_config.error_field
    success_condition = metric_config.success_condition

    # 获取成功条件
    success_codes_str = ",".join(str(code) for code in metric_config.success_codes)
    exclude_codes_str = ",".join(str(code) for code in metric_config.exclude_codes)

    # 构建时间过滤条件，根据粒度调整时间精度
    time_format = "%Y-%m-%d"
    if table_granularity in [
        TableGranularity.MIN_1,
        TableGranularity.MIN_10,
        TableGranularity.HOUR_1,
    ]:
        time_format = "%Y-%m-%d %H:%M:%S"

    # 构建WHERE条件
    where_conditions = [
        f"{time_field} BETWEEN '{start_time.strftime(time_format)}' AND '{end_time.strftime(time_format)}'",
        f"{dimension} = '{dimension_value}'",
    ]

    # 添加配置中的固定过滤条件
    for field, value in metric_config.filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加用户指定的过滤条件
    for field, value in filters.items():
        if field == "__custom_filter__":
            # 处理自定义过滤器
            for line in str(value).strip().split("\n"):
                line = line.strip()
                if line:  # 跳过空行
                    where_conditions.append(line)
        elif field in metric_config.dimensions:
            # 处理常规过滤器
            if isinstance(value, (list, tuple)):
                where_conditions.append(f"{field} IN ({','.join(repr(v) for v in value)})")
            else:
                where_conditions.append(f"{field} = {repr(value)}")

    where_clause = "WHERE " + " AND ".join(where_conditions)

    # 在SQL注释中标明使用的表粒度，但放在WITH之后
    granularity_comment = f"/* 使用{TableGranularity.get_display_name(table_granularity)}粒度表 */"

    # 构建SQL - 计算剔除某个维度后的成功率
    return f"""
    WITH {granularity_comment} daily_excluded AS (
        SELECT 
            {time_field},
            {excluded_dim},
            SUM({count_field}) as total_count,
            SUM(if({error_field} in ({success_codes_str}{f' and {success_condition}' if success_condition else ''}), {count_field}, 0)) as success_count,
            SUM(if({error_field} not in ({exclude_codes_str}), {count_field}, 0)) as non_blacklist_count
        FROM {table_name}
        {where_clause}
        GROUP BY {time_field}, {excluded_dim}
    ),
    daily_totals AS (
        SELECT 
            {time_field},
            SUM(total_count) as overall_total,
            SUM(success_count) as overall_success,
            SUM(non_blacklist_count) as overall_non_blacklist,
            ROUND(SUM(success_count) / SUM(total_count) * 100, 2) as overall_success_rate,
            ROUND(SUM(success_count) / SUM(non_blacklist_count) * 100, 2) as overall_exclude_success_rate
        FROM 
            daily_excluded
        GROUP BY 
            {time_field}
    ),
    daily_top_values AS (
        SELECT 
            {excluded_dim},
            SUM(total_count) as total
        FROM daily_excluded
        GROUP BY {excluded_dim}
        ORDER BY total DESC
        LIMIT 10
    ),
    excluded_stats AS (
        SELECT 
            de.{time_field},
            de.{excluded_dim},
            de.total_count,
            de.success_count,
            de.non_blacklist_count,
            dt.overall_total - de.total_count as excluded_total_count,
            dt.overall_success - de.success_count as excluded_success_count,
            dt.overall_non_blacklist - de.non_blacklist_count as excluded_non_blacklist_count,
            dt.overall_success_rate,
            dt.overall_exclude_success_rate,
            CASE 
                WHEN (dt.overall_total - de.total_count) > 0 
                THEN ROUND((dt.overall_success - de.success_count) / (dt.overall_total - de.total_count) * 100, 2)
                ELSE 0 
            END as excluded_success_rate,
            CASE 
                WHEN (dt.overall_non_blacklist - de.non_blacklist_count) > 0 
                THEN ROUND((dt.overall_success - de.success_count) / (dt.overall_non_blacklist - de.non_blacklist_count) * 100, 2)
                ELSE 0 
            END as excluded_exclude_success_rate,
            ROUND(de.total_count / dt.overall_total * 100, 2) as percentage_of_total
        FROM 
            daily_excluded de
        INNER JOIN
            daily_totals dt ON de.{time_field} = dt.{time_field}
        INNER JOIN
            daily_top_values tv ON de.{excluded_dim} = tv.{excluded_dim}
    )
    SELECT 
        {time_field},
        {excluded_dim},
        total_count,
        success_count,
        excluded_total_count,
        excluded_success_count,
        CAST(overall_success_rate AS CHAR) as overall_success_rate,
        CAST(excluded_success_rate AS CHAR) as excluded_success_rate,
        CAST(overall_exclude_success_rate AS CHAR) as overall_exclude_success_rate,
        CAST(excluded_exclude_success_rate AS CHAR) as excluded_exclude_success_rate,
        percentage_of_total,
        excluded_exclude_success_rate - overall_exclude_success_rate as exclude_success_rate_diff
    FROM 
        excluded_stats
    ORDER BY 
        {time_field}, total_count DESC
    """
