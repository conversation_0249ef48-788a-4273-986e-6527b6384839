from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union


# 简化的表粒度管理
class TableGranularityManager:
    """表粒度管理器 - 简化版本"""

    # 表后缀映射
    GRANULARITY_SUFFIXES = {
        "1min": "",  # 1分钟粒度，无后缀
        "10min": "_10m",  # 10分钟粒度
        "1hour": "_1h",  # 1小时粒度
        "1day": "_1d",  # 1天粒度
    }

    @classmethod
    def get_table_name(cls, base_table: str, granularity: str = "1day") -> str:
        """
        根据粒度获取表名

        Args:
            base_table: 基础表名（默认应该是天粒度的表名，以_1d结尾）
            granularity: 粒度类型 ("1min", "10min", "1hour", "1day")

        Returns:
            对应粒度的表名
        """
        if granularity not in cls.GRANULARITY_SUFFIXES:
            raise ValueError(f"不支持的粒度: {granularity}")

        # 如果表名以_1d结尾，则替换为对应的后缀
        if base_table.endswith("_1d"):
            base_name = base_table[:-3]  # 移除_1d
            return f"{base_name}{cls.GRANULARITY_SUFFIXES[granularity]}"

        # 否则直接添加后缀
        return f"{base_table}{cls.GRANULARITY_SUFFIXES[granularity]}"

    @classmethod
    def auto_select_granularity(cls, start_time: datetime, end_time: datetime) -> str:
        """
        根据时间范围自动选择表粒度

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            粒度类型字符串
        """
        time_diff = end_time - start_time

        if time_diff > timedelta(days=3):
            return "1day"
        elif time_diff > timedelta(days=1):
            return "1hour"
        elif time_diff >= timedelta(hours=12):
            return "10min"
        else:
            return "1min"


# 为了向后兼容，保留一些原有的枚举和函数（标记为废弃）
class TableGranularity(Enum):
    """表粒度类型 - 已废弃，使用 TableGranularityManager 替代"""

    MIN_1 = ""
    MIN_10 = "_10m"
    HOUR_1 = "_1h"
    DAY_1 = "_1d"


def get_table_granularity(
    start_time: datetime,
    end_time: datetime,
    override_granularity: Optional[TableGranularity] = None,
) -> TableGranularity:
    if override_granularity:
        return override_granularity

    granularity_str = TableGranularityManager.auto_select_granularity(start_time, end_time)
    granularity_mapping = {
        "1min": TableGranularity.MIN_1,
        "10min": TableGranularity.MIN_10,
        "1hour": TableGranularity.HOUR_1,
        "1day": TableGranularity.DAY_1,
    }
    return granularity_mapping[granularity_str]
