"""
缓存管理模块
负责管理应用中的各种数据缓存，提供统一的缓存接口
"""

import hashlib
import json
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Optional, Tuple


class CacheManager:
    """缓存管理类，为应用提供缓存服务"""

    def __init__(self):
        """初始化缓存管理器"""
        self.cache_dict = {}
        self.cache_date = datetime.now().date()

    def _check_and_clear_daily_cache(self):
        """检查并清理每日缓存"""
        current_date = datetime.now().date()

        # 如果日期发生变化，清理缓存
        if self.cache_date != current_date:
            print(f"缓存已清理：日期从 {self.cache_date} 更新为 {current_date}")
            self.cache_dict = {}
            self.cache_date = current_date

    def _generate_cache_key(self, query: str, params: Dict[str, Any], prefix: str = "query") -> str:
        """
        生成缓存键

        Args:
            query: SQL查询语句
            params: 查询参数
            prefix: 缓存键前缀

        Returns:
            生成的缓存键
        """
        # 将查询和参数组合成字符串
        cache_input = f"{query}_{json.dumps(params, sort_keys=True, default=str)}"

        # 生成MD5哈希作为缓存键
        cache_key = f"{prefix}_{hashlib.md5(cache_input.encode()).hexdigest()}"

        return cache_key

    def get_cached_data(self, cache_key: str) -> Optional[Any]:
        """
        获取缓存数据

        Args:
            cache_key: 缓存键

        Returns:
            缓存的数据，如果不存在则返回None
        """
        self._check_and_clear_daily_cache()
        return self.cache_dict.get(cache_key)

    def set_cached_data(self, cache_key: str, data: Any, ttl_hours: int = 24) -> None:
        """
        设置缓存数据

        Args:
            cache_key: 缓存键
            data: 要缓存的数据
            ttl_hours: 缓存有效期（小时），暂未实现TTL功能
        """
        self._check_and_clear_daily_cache()
        self.cache_dict[cache_key] = data
        print(f"数据已缓存，键: {cache_key}")

    def get_or_compute(self, cache_key: str, compute_func: Callable, *args, **kwargs) -> Any:
        """
        获取缓存数据或计算新数据

        Args:
            cache_key: 缓存键
            compute_func: 计算函数
            *args: 传递给计算函数的位置参数
            **kwargs: 传递给计算函数的关键字参数

        Returns:
            缓存的数据或新计算的数据
        """
        cached_data = self.get_cached_data(cache_key)
        if cached_data is not None:
            print(f"使用缓存数据，键: {cache_key}")
            return cached_data

        # 计算新数据
        print(f"计算新数据，键: {cache_key}")
        new_data = compute_func(*args, **kwargs)

        # 缓存新数据
        self.set_cached_data(cache_key, new_data)

        return new_data

    def clear_cache_by_prefix(self, prefix: str) -> None:
        """
        根据前缀清理缓存

        Args:
            prefix: 缓存键前缀
        """
        keys_to_remove = [k for k in self.cache_dict.keys() if k.startswith(f"{prefix}_")]
        for key in keys_to_remove:
            del self.cache_dict[key]
        print(f"已清理前缀为 '{prefix}' 的缓存，共 {len(keys_to_remove)} 项")

    def clear_all_cache(self) -> None:
        """清理所有缓存"""
        self.cache_dict = {}
        self.cache_date = datetime.now().date()
        print("所有缓存已清理")


# 全局缓存管理器实例
cache_manager = CacheManager()
