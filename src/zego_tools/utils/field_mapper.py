"""
字段映射工具，用于UI显示名称与数据库字段名称之间的转换
"""

from typing import Any, Dict, List, Optional, Union


class FieldMapper:
    """
    字段映射类，用于管理UI显示名称与数据库字段名称之间的映射关系
    """

    def __init__(self):
        # 显示名称 -> 数据库字段名称的映射
        self._display_to_db: Dict[str, str] = {}

        # 数据库字段名称 -> 显示名称的映射
        self._db_to_display: Dict[str, str] = {}

    def register(self, display_name: str, db_field_name: str) -> None:
        """
        注册一个字段映射

        Args:
            display_name: UI显示名称
            db_field_name: 数据库字段名称
        """
        self._display_to_db[display_name] = db_field_name
        self._db_to_display[db_field_name] = display_name

    def register_multiple(self, mapping: Dict[str, str]) -> None:
        """
        批量注册多个字段映射

        Args:
            mapping: 显示名称到数据库字段名称的映射字典
        """
        for display_name, db_field_name in mapping.items():
            self.register(display_name, db_field_name)

    def get_db_field_name(self, display_name: str) -> str:
        """
        根据显示名称获取数据库字段名称

        Args:
            display_name: UI显示名称

        Returns:
            对应的数据库字段名称，如果不存在映射则返回原名称
        """
        return self._display_to_db.get(display_name, display_name)

    def get_display_name(self, db_field_name: str) -> str:
        """
        根据数据库字段名称获取显示名称

        Args:
            db_field_name: 数据库字段名称

        Returns:
            对应的UI显示名称，如果不存在映射则返回原名称
        """
        return self._db_to_display.get(db_field_name, db_field_name)

    def convert_dict_keys(
        self, data: Dict[str, Any], to_db: bool = True
    ) -> Dict[str, Any]:
        """
        转换字典的键

        Args:
            data: 要转换的字典
            to_db: 是否转换为数据库字段名（True）或显示名称（False）

        Returns:
            转换后的新字典
        """
        result = {}
        for key, value in data.items():
            if to_db:
                new_key = self.get_db_field_name(key)
            else:
                new_key = self.get_display_name(key)
            result[new_key] = value
        return result

    def convert_df_columns(self, df, to_display: bool = True) -> None:
        """
        转换DataFrame的列名（就地修改）

        Args:
            df: pandas DataFrame对象
            to_display: 是否转换为显示名称（True）或数据库字段名（False）
        """
        if df is None or df.empty:
            return

        rename_dict = {}
        for col in df.columns:
            if to_display:
                rename_dict[col] = self.get_display_name(col)
            else:
                rename_dict[col] = self.get_db_field_name(col)

        # 重命名列
        df.rename(columns=rename_dict, inplace=True)

    def convert_list(self, items: List[str], to_db: bool = True) -> List[str]:
        """
        转换字符串列表

        Args:
            items: 要转换的字符串列表
            to_db: 是否转换为数据库字段名（True）或显示名称（False）

        Returns:
            转换后的新列表
        """
        if to_db:
            return [self.get_db_field_name(item) for item in items]
        else:
            return [self.get_display_name(item) for item in items]


# 创建全局字段映射器实例
field_mapper = FieldMapper()

# 初始化常用字段映射
field_mapper.register_multiple(
    {
        "AppID": "app_id",
        "国家": "country",
        "SDK版本": "sdk_version",
        "操作系统": "os_type",
        "平台": "platform",
        "拉流源": "src",
        "运营商": "isp",
        "城市": "city",
        "地区": "region",
        "错误码": "error_code",
        "错误": "error",
        "事件": "event",
        "时间戳": "timestamp",
        "日期": "timestamp",
        "成功率": "success_rate",
        "剔除黑名单后成功率": "exclude_success_rate",
        "总请求数": "total_count",
        "成功请求数": "success_count",
        "失败请求数": "fail_count",
        "错误次数": "error_count",
        "错误百分比": "error_percentage",
        "是否黑名单": "is_excluded",
        "指标值": "metric_value",
    }
)


def get_db_field_name(display_name: str) -> str:
    """
    根据显示名称获取数据库字段名称（便捷函数）

    Args:
        display_name: UI显示名称

    Returns:
        对应的数据库字段名称
    """
    return field_mapper.get_db_field_name(display_name)


def get_display_name(db_field_name: str) -> str:
    """
    根据数据库字段名称获取显示名称（便捷函数）

    Args:
        db_field_name: 数据库字段名称

    Returns:
        对应的UI显示名称
    """
    return field_mapper.get_display_name(db_field_name)


def convert_db_to_display_names(df) -> None:
    """
    将DataFrame的列名从数据库字段名转换为显示名称（便捷函数）

    Args:
        df: pandas DataFrame对象
    """
    field_mapper.convert_df_columns(df, to_display=True)


def convert_display_to_db_names(df) -> None:
    """
    将DataFrame的列名从显示名称转换为数据库字段名（便捷函数）

    Args:
        df: pandas DataFrame对象
    """
    field_mapper.convert_df_columns(df, to_display=False)
