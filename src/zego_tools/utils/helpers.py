"""工具函数模块，提供各种辅助功能"""

import re
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

import pandas as pd
import sqlparse

# 全局变量用于存储状态，替代streamlit session_state
_global_state = {}


def format_percentage(value: float) -> str:
    """
    格式化百分比

    Args:
        value: 要格式化的百分比值

    Returns:
        格式化后的百分比字符串
    """
    if pd.isna(value):
        return "N/A"
    return f"{value:.2f}%"


def format_sql(sql: str) -> str:
    """
    格式化SQL语句，保持与自动分析系统一致的格式

    Args:
        sql: 原始SQL语句

    Returns:
        格式化后的SQL语句
    """
    try:
        # 使用sqlparse格式化SQL
        formatted = sqlparse.format(
            sql,
            reindent=True,
            keyword_case="upper",
            identifier_case="lower",
            strip_comments=False,
            indent_width=2,
        )
        return formatted
    except Exception as e:
        print(f"SQL格式化失败: {e}")
        return sql


def truncate_label(label: str, max_length: int = 30) -> str:
    """
    截断过长的标签文本

    Args:
        label: 原始标签文本
        max_length: 最大长度限制，默认30

    Returns:
        处理后的标签文本
    """
    label_str = str(label)
    if len(label_str) > max_length:
        return label_str[: max_length - 3] + "..."
    return label_str


def clean_country_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    清洗国家数据，去除无效的国家代码

    Args:
        df: 包含国家数据的DataFrame

    Returns:
        清洗后的DataFrame
    """
    if "country" not in df.columns:
        return df

    # 过滤掉空值、"未知"、"-"等无效国家代码
    invalid_countries = ["", "未知", "-", "unknown", "null", None]
    df_clean = df[~df["country"].isin(invalid_countries)]
    df_clean = df_clean.dropna(subset=["country"])

    return df_clean


def extract_numeric_from_string(text: str) -> Optional[float]:
    """
    从字符串中提取数字

    Args:
        text: 包含数字的字符串

    Returns:
        提取的数字，如果无法提取则返回None
    """
    if not isinstance(text, str):
        return None

    # 使用正则表达式提取数字
    pattern = r"[-+]?(?:\d*\.*\d+)"
    match = re.search(pattern, text)

    if match:
        try:
            return float(match.group())
        except ValueError:
            return None

    return None


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误

    Args:
        numerator: 分子
        denominator: 分母
        default: 当分母为0时的默认值

    Returns:
        除法结果或默认值
    """
    if denominator == 0 or pd.isna(denominator):
        return default
    return numerator / denominator


def get_or_set_state(key: str, default_value: Any) -> Any:
    """
    获取或设置全局状态值，替代streamlit的session_state

    Args:
        key: 状态键
        default_value: 默认值

    Returns:
        状态值
    """
    if key not in _global_state:
        _global_state[key] = default_value
    return _global_state[key]


def set_state(key: str, value: Any) -> None:
    """
    设置全局状态值

    Args:
        key: 状态键
        value: 要设置的值
    """
    _global_state[key] = value


def clear_state() -> None:
    """清除所有全局状态"""
    _global_state.clear()
