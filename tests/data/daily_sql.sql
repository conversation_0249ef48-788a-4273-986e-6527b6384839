-- 自动生成的 SQL 测试集合
-- 生成时间: 2025-08-05 11:06:20
-- 总计 175 个 SQL 语句

-- 004_拉流成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 008_拉流成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 010_拉流成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 014_拉流成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 016_拉流成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 020_拉流成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 024_拉流成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 026_拉流成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 030_拉流成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 032_拉流成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042) AND country = '中国' AND event = 'play' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1104001,1000014,1103049,12102001,12301004,12301011,12301014,32001004,63000001,63000002,1002055,1104042)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 036_登录成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 040_登录成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 042_登录成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 046_登录成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 048_登录成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 052_登录成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 056_登录成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 058_登录成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 062_登录成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 064_登录成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 068_5s登录成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 072_5s登录成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 074_5s登录成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 078_5s登录成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 080_5s登录成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt_5s ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 084_5s登录成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 088_5s登录成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 090_5s登录成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 094_5s登录成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND app_id = '1797223084' AND country = '美国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 096_5s登录成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011) AND country = '中国' AND event = 'login' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1100001,1002001,1002011,1002012,1002013,1002005,1002006,1002007,1002056,1002018,1002014,50001011)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 100_推流成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 104_推流成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 106_推流成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 110_推流成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 112_推流成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 116_推流成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 120_推流成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 122_推流成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 126_推流成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 128_推流成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002) AND country = '中国' AND event = 'publish' AND error NOT IN (10001001,10009200,10009201,10009202,60001041,60001042,60001043,60001044,60001045,1103001,1103044,1103099,1000002,12301011,63000002,52001012,63000001,12301014,15000002)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 132_视频卡顿率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
        SUM(video_duration_sum) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 136_视频卡顿率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
            SUM(video_duration_sum) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 138_视频卡顿率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
            SUM(video_duration_sum) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 142_视频卡顿率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
            SUM(video_duration_sum) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 144_视频卡顿率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(if(sum(video_duration_sum) = 0 or sum(video_duration_sum)<0 or sum(video_break_duration_sum) <0, 0, sum(video_break_duration_sum)/sum(video_duration_sum)),4) as metric_value,
            SUM(video_duration_sum) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 148_拉流丢包率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 152_拉流丢包率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 154_拉流丢包率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 158_拉流丢包率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 160_拉流丢包率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 164_推流丢包率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
        SUM(plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 168_推流丢包率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 170_推流丢包率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 174_推流丢包率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 176_推流丢包率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(if(sum(plr_cnt) = 0, -1, sum(plr_sum) / sum(plr_cnt)),4) as metric_value,
            SUM(plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_publish_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 180_统一接入request成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 184_统一接入request成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 186_统一接入request成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 190_统一接入request成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 192_统一接入request成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 196_统一接入request成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 200_统一接入request成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 202_统一接入request成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 206_统一接入request成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 208_统一接入request成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'zegoconn_request' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 212_统一接入connect成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 216_统一接入connect成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 218_统一接入connect成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 222_统一接入connect成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 224_统一接入connect成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error = 0 AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 228_统一接入connect成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 232_统一接入connect成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 234_统一接入connect成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 238_统一接入connect成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 240_统一接入connect成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'zegoconn_connect' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 244_3s拉流请求成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 248_3s拉流请求成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 250_3s拉流请求成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 254_3s拉流请求成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 256_3s拉流请求成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12102001,12301004,52001105,52001104) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 260_3s拉流请求成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 264_3s拉流请求成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 266_3s拉流请求成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 270_3s拉流请求成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 272_3s拉流请求成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 276_3s推流请求成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 280_3s推流请求成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 282_3s推流请求成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 286_3s推流请求成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 288_3s推流请求成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error IN (0,1002034,1002050,1002055,12301011,52001012,52001105) AND is_over_time = 0 THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 292_3s推流请求成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 296_3s推流请求成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 298_3s推流请求成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 302_3s推流请求成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 304_3s推流请求成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'publish' AND type = 'request' AND coalesce(extlib_type,0) = 0 AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 308_推流rtc子事件成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 312_推流rtc子事件成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 314_推流rtc子事件成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 318_推流rtc子事件成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 320_推流rtc子事件成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 324_推流rtc子事件成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 328_推流rtc子事件成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 330_推流rtc子事件成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 334_推流rtc子事件成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 336_推流rtc子事件成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'publish' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 340_拉流rtc子事件成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 344_拉流rtc子事件成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 346_拉流rtc子事件成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 350_拉流rtc子事件成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 352_拉流rtc子事件成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 356_拉流rtc子事件成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 360_拉流rtc子事件成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 362_拉流rtc子事件成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 366_拉流rtc子事件成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 368_拉流rtc子事件成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'rtc' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 372_拉流l3子事件成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 376_拉流l3子事件成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 378_拉流l3子事件成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 382_拉流l3子事件成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 384_拉流l3子事件成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 388_拉流l3子事件成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 392_拉流l3子事件成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 394_拉流l3子事件成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 398_拉流l3子事件成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND app_id = '1797223084' AND country = '美国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 400_拉流l3子事件成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_keysucrate_app_country_platform_idname_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888) AND country = '中国' AND event = 'play' AND type = 'l3' AND error NOT IN (666, 777, 888)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 404_拉流调度成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 408_拉流调度成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 410_拉流调度成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 414_拉流调度成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 416_拉流调度成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 420_拉流调度成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 424_拉流调度成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 426_拉流调度成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 430_拉流调度成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 432_拉流调度成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND event = 'play_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 436_推流调度成功率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
        SUM(err_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 440_推流调度成功率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 442_推流调度成功率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 446_推流调度成功率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 448_推流调度成功率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(SUM(CASE WHEN error in (0,10007010) THEN err_cnt ELSE 0 END) * 100.0 / SUM(err_cnt), 2) as metric_value,
            SUM(err_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 452_推流调度成功率_错误码分布_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
    error as error_code,
    SUM(err_cnt) as error_count,
    ROUND(SUM(err_cnt) * 100.0 / (
        SELECT SUM(err_cnt) 
        FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d 
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    ), 2) as error_rate
FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
GROUP BY error
ORDER BY error_count DESC
LIMIT 20

-- 456_推流调度成功率_错误码分布_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        sdk_version,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, sdk_version
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.sdk_version,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 458_推流调度成功率_错误码分布_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        country,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, country
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.country,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 462_推流调度成功率_错误码分布_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        isp,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND app_id = '1797223084' AND country = '美国' AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, isp
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.isp,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 464_推流调度成功率_错误码分布_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH error_stats AS (
    SELECT 
        error as error_code,
        app_id,
        SUM(err_cnt) as error_count
    FROM ocean.realtime_ads_speedlog_event_app_country_region_city_isp_platform_src_sdkversion_ostype_error_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004) AND country = '中国' AND event = 'publish_dispatch' AND error NOT IN (32001004, 35500011, 35500006, 35500013, 35500004)
    GROUP BY error, app_id
),
total_by_error AS (
    SELECT 
        error_code,
        SUM(error_count) as total_error_count
    FROM error_stats
    GROUP BY error_code
),
top_errors AS (
    SELECT error_code
    FROM total_by_error
    ORDER BY total_error_count DESC
    LIMIT 10
)
SELECT 
    es.error_code,
    es.app_id,
    es.error_count,
    ROUND(es.error_count * 100.0 / tbe.total_error_count, 2) as dimension_rate_in_error
FROM error_stats es
INNER JOIN total_by_error tbe ON es.error_code = tbe.error_code
INNER JOIN top_errors te ON es.error_code = te.error_code
ORDER BY tbe.total_error_count DESC, es.error_count DESC

-- 468_视频首帧_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
        SUM(fft_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 472_视频首帧_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
            SUM(fft_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 474_视频首帧_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
            SUM(fft_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 478_视频首帧_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
            SUM(fft_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 480_视频首帧_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(sum(fft_sum)/sum(fft_cnt),4) as metric_value,
            SUM(fft_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_fft_app_event_platform_src_country_region_city_isp_protocol_sdkversion_ostype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND event = 'sdk_play_decode_first_video_frame' AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 484_拉流请求耗时均值_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        sum(play_request_num)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 488_拉流请求耗时均值_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            sum(play_request_num)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 490_拉流请求耗时均值_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            sum(play_request_num)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 494_拉流请求耗时均值_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            sum(play_request_num)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 496_拉流请求耗时均值_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            sum(play_request_num)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 500_推流请求耗时均值_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 504_推流请求耗时均值_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 506_推流请求耗时均值_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 510_推流请求耗时均值_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 512_推流请求耗时均值_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            sum(pub_request_num)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 516_拉流请求耗时1s占比_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
        SUM(play_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 520_拉流请求耗时1s占比_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 522_拉流请求耗时1s占比_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 526_拉流请求耗时1s占比_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 528_拉流请求耗时1s占比_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            sum(play_request_0_100+play_request_100_200+play_request_200_400+play_request_400_600+play_request_600_800+play_request_800_1000)/sum(play_request_cnt) as metric_value,
            SUM(play_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 532_推流请求耗时1s占比_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
        SUM(pub_request_cnt) as total_count
    FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 536_推流请求耗时1s占比_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 538_推流请求耗时1s占比_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 542_推流请求耗时1s占比_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 544_推流请求耗时1s占比_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            sum(pub_request_0_100+pub_request_100_200+pub_request_200_400+pub_request_400_600+pub_request_600_800+pub_request_800_1000)/sum(pub_request_cnt) as metric_value,
            SUM(pub_request_cnt) as total_count
        FROM ocean.realtime_ads_speedlog_consumed_app_platform_country_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND coalesce(extlib_type,0) = 0 AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 548_端到端丢包率_默认指标_指标趋势分析_1day_AppID=1797223084_Country=美国
SELECT 
        timestamp,
        ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
        SUM(peer_to_peer_plr_cnt) as total_count
    FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
    WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国'
    GROUP BY timestamp
    ORDER BY timestamp

-- 552_端到端丢包率_默认指标_SDK版本趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            sdk_version,
            ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
            SUM(peer_to_peer_plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, sdk_version
    ),
    top_dimensions AS (
        SELECT 
            sdk_version,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY sdk_version
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.sdk_version,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.sdk_version = td.sdk_version
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 554_端到端丢包率_默认指标_国家维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            country,
            ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
            SUM(peer_to_peer_plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND app_id = '1797223084'
        GROUP BY timestamp, country
    ),
    top_dimensions AS (
        SELECT 
            country,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY country
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.country,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.country = td.country
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 558_端到端丢包率_默认指标_运营商趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            isp,
            ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
            SUM(peer_to_peer_plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND app_id = '1797223084' AND country = '美国' AND app_id = '1797223084'
        GROUP BY timestamp, isp
    ),
    top_dimensions AS (
        SELECT 
            isp,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY isp
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.isp,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.isp = td.isp
    ORDER BY ds.timestamp, td.dimension_total DESC

-- 560_端到端丢包率_默认指标_客户维度趋势分析_1day_AppID=1797223084_Country=美国
WITH daily_stats AS (
        SELECT 
            timestamp,
            app_id,
            ROUND(if(sum(peer_to_peer_plr_cnt) = 0, -1, sum(peer_to_peer_plr_sum) / sum(peer_to_peer_plr_cnt)),2) as metric_value,
            SUM(peer_to_peer_plr_cnt) as total_count
        FROM ocean.realtime_ads_streamreport_play_app_platform_src_country_region_city_isp_sdkversion_ostype_protocol_networktype_1d
        WHERE timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 10 DAY) AND NOW() AND country = '中国' AND country = '中国'
        GROUP BY timestamp, app_id
    ),
    top_dimensions AS (
        SELECT 
            app_id,
            SUM(total_count) as dimension_total
        FROM daily_stats
        GROUP BY app_id
        ORDER BY dimension_total DESC
        LIMIT 10
    )
    SELECT 
        ds.timestamp,
        ds.app_id,
        ds.metric_value,
        ds.total_count
    FROM daily_stats ds
    INNER JOIN top_dimensions td ON ds.app_id = td.app_id
    ORDER BY ds.timestamp, td.dimension_total DESC

