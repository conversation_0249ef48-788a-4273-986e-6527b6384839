# 0725 react loop的模式

good:
已经能够分析定位到问题

bad:
1. 纯串行，成功率-错误码-sdk趋势-isp趋势，当发现sdk趋势有问题后，没有继续其他维度分析
问题1：会导致结论可能失真（比如，可能其他维度问题更明显，但被其他维度打断）
问题2：执行较慢、token消耗过高

TODO:
—— 优化agent结构、流程结束判定：
    1. 将不同维度的下钻分配到并行任务节点
    2. 即使单维度定位到问题，也需要分析其他维度，然后聚合每个维度的结论
—— 优化上下文控制，降低token消耗：
    1. 不同分析维度专注上级数据和当前数据
    2. 总结节点聚合结论

# 0728 并行架构

1. 并行分析架构重构
2. 数据预查询优化 —— 提前查询数据并提供给llm，降低50%请求次数，提高流程成功率
3. 黑名单错误码 逻辑的优化，避免归因到10007010这种非故障错误码中
4. 错误码含义，查询错误码的数据表，将错误码含义也添加到df列中






