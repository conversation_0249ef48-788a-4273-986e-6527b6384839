
------------ 测试 ------------
1. 问题库
2. 批量执行，结果输出到表
3. 不同模型并行测试，人工对比结果


------------ Core-核心 ------------
数据相关：对比 metrics_dashboard 
P1 数据的二次加工、二次过滤
- 错误码查询，计算一份成功率数据
- 过滤请求量过低的维度

P2 2. 若集成到飞书，需要生成趋势图，让人review。 另外模型支持多模态后，也可以将图形一起给到llm



------------ UI-前端 ------------
可调试、可观测
- P0 前端 （ 试下能否直接用langdev ）
- P0 进度恢复 （ 试下能否直接用 langdev, 不行的话尝试使用pg数据库？。 长远考虑还是用pg）


------------ Project-工程化 ------------

- P1 接入飞书机器人




